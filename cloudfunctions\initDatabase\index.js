// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
  traceUser: true
})

const db = cloud.database()

/**
 * 数据库初始化云函数
 * 创建必要的数据库集合和索引
 * @param {Object} event - 云函数调用参数
 * @param {Object} context - 云函数上下文
 * @returns {Object} 初始化结果
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    console.log('开始初始化数据库...')
    
    const results = []
    
    // 1. 创建 reminders 集合（如果不存在）
    try {
      // 尝试向 reminders 集合添加一条测试记录
      const testReminder = await db.collection('reminders').add({
        data: {
          _test: true,
          createTime: db.serverDate()
        }
      })
      
      // 立即删除测试记录
      await db.collection('reminders').doc(testReminder._id).remove()
      
      results.push({
        collection: 'reminders',
        status: 'exists',
        message: 'reminders 集合已存在'
      })
      
    } catch (error) {
      if (error.errCode === -502005) {
        // 集合不存在，需要创建
        console.log('reminders 集合不存在，正在创建...')
        
        // 通过添加一条记录来创建集合
        const createResult = await db.collection('reminders').add({
          data: {
            _init: true,
            cardId: 'init',
            reminderTime: db.serverDate(),
            status: 'init',
            createTime: db.serverDate(),
            type: 'init',
            _openid: wxContext.OPENID || 'system'
          }
        })
        
        // 删除初始化记录
        await db.collection('reminders').doc(createResult._id).remove()
        
        results.push({
          collection: 'reminders',
          status: 'created',
          message: 'reminders 集合创建成功'
        })
      } else {
        throw error
      }
    }
    
    // 2. 检查其他必要集合
    const collections = ['users', 'cards', 'tags']
    
    for (const collectionName of collections) {
      try {
        // 尝试查询集合
        await db.collection(collectionName).limit(1).get()
        results.push({
          collection: collectionName,
          status: 'exists',
          message: `${collectionName} 集合已存在`
        })
      } catch (error) {
        if (error.errCode === -502005) {
          results.push({
            collection: collectionName,
            status: 'missing',
            message: `${collectionName} 集合不存在，请在云开发控制台手动创建`
          })
        } else {
          results.push({
            collection: collectionName,
            status: 'error',
            message: `检查 ${collectionName} 集合时出错: ${error.message}`
          })
        }
      }
    }
    
    console.log('数据库初始化完成')
    
    return {
      success: true,
      message: '数据库初始化完成',
      data: {
        results: results,
        timestamp: new Date().toISOString()
      }
    }
    
  } catch (error) {
    console.error('数据库初始化失败', error)
    return {
      success: false,
      message: '数据库初始化失败',
      error: error.message || String(error),
      errorCode: error.errCode || -1
    }
  }
}
