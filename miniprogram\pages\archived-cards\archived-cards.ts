/**
 * 归档卡片列表页面逻辑
 * 显示用户的所有已归档记忆卡片，支持标签筛选和恢复卡片
 */
Page({
  data: {
    cards: [] as any[],
    tagOptions: [] as any[],
    activeTagId: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.checkLoginStatus();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.checkLoginStatus();
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const app = getApp<IAppOption>();

    // 检查全局登录状态
    if (!app.globalData.isLoggedIn) {
      // 尝试从本地存储获取用户信息
      wx.getStorage({
        key: 'userInfo',
        success: (res) => {
          app.globalData.userInfo = res.data;
          app.globalData.isLoggedIn = true;
          this.loadData();
        },
        fail: () => {
          // 未登录，跳转到登录页
          wx.redirectTo({
            url: '/pages/login/login'
          });
        }
      });
    } else {
      this.loadData();
    }
  },

  /**
   * 加载页面数据
   */
  loadData() {
    this.loadTags();
    this.loadArchivedCards();
  },

  /**
   * 加载用户的所有标签
   */
  loadTags() {
    // 显示加载中
    wx.showLoading({ title: '加载中' });

    // 调用云函数获取标签列表
    wx.cloud.callFunction({
      name: 'getTags',
      success: (res: any) => {
        console.log('获取标签成功', res);

        if (res.result && res.result.success) {
          const tags = res.result.data || [];

          // 构建下拉菜单选项
          const options = [
            { label: '全部卡片', value: '' },
            ...tags.map((tag: any) => ({
              label: tag.name,
              value: tag._id
            }))
          ];

          this.setData({
            tagOptions: options
          });
        } else {
          console.error('获取标签失败', res.result);
          this.setData({
            tagOptions: [{ label: '全部卡片', value: '' }]
          });
        }
      },
      fail: (err: any) => {
        console.error('获取标签失败', err);
        wx.showToast({
          title: '获取标签失败',
          icon: 'error'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 加载归档卡片列表
   */
  loadArchivedCards() {
    // 显示加载中
    wx.showLoading({ title: '加载中' });

    // 构建查询参数
    const params: any = {
      archived: true // 只获取已归档的卡片
    };

    // 如果选择了特定标签，添加标签筛选
    if (this.data.activeTagId) {
      params.tagId = this.data.activeTagId;
    }

    // 调用云函数获取归档卡片列表
    wx.cloud.callFunction({
      name: 'getCards',
      data: params,
      success: (res: any) => {
        console.log('获取归档卡片成功', res);

        if (res.result && res.result.success) {
          const cards = res.result.data || [];

          // 处理卡片数据，格式化日期和内容摘要
          const formattedCards = cards.map((card: any) => {
            return {
              ...card,
              id: card._id, // 确保有id字段
              createTime: this.formatDate(card.createTime),
              content: this.truncateContent(card.content),
              archivedTime: this.formatDate(card.archivedTime)
            };
          });

          this.setData({
            cards: formattedCards
          });
        } else {
          console.error('获取归档卡片失败', res.result);
          this.setData({
            cards: []
          });
          wx.showToast({
            title: res.result?.message || '获取归档卡片失败',
            icon: 'error'
          });
        }
      },
      fail: (err: any) => {
        console.error('获取归档卡片失败', err);
        wx.showToast({
          title: '获取归档卡片失败',
          icon: 'error'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 处理标签筛选变化
   */
  onTagChange(e: any) {
    const tagId = e.detail.value;
    this.setData({
      activeTagId: tagId
    });

    // 重新加载归档卡片列表
    this.loadArchivedCards();
  },

  /**
   * 处理卡片点击事件
   */
  onCardTap(e: any) {
    const cardId = e.currentTarget.dataset.id;
    const cardIndex = e.currentTarget.dataset.index;
    const card = this.data.cards[cardIndex];

    // 显示操作选择弹窗
    this.showCardActionSheet(cardId, card);
  },

  /**
   * 显示卡片操作选择弹窗
   */
  showCardActionSheet(cardId: string, card: any) {
    const itemList = ['查看卡片', '恢复卡片', '永久删除'];
    
    wx.showActionSheet({
      itemList: itemList,
      success: (res) => {
        if (res.tapIndex === 0) {
          // 查看卡片
          wx.navigateTo({
            url: `/pages/card-edit/card-edit?id=${cardId}&readonly=true`
          });
        } else if (res.tapIndex === 1) {
          // 恢复卡片
          this.restoreCard(cardId);
        } else if (res.tapIndex === 2) {
          // 永久删除
          this.confirmDeleteCard(cardId);
        }
      }
    });
  },

  /**
   * 恢复卡片
   */
  restoreCard(cardId: string) {
    wx.showModal({
      title: '确认恢复',
      content: '确定要恢复这张卡片吗？恢复后将重新开始复习计划。',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '恢复中' });

          wx.cloud.callFunction({
            name: 'restoreCard',
            data: { cardId },
            success: (res: any) => {
              console.log('恢复卡片成功', res);

              if (res.result && res.result.success) {
                wx.showToast({
                  title: '恢复成功',
                  icon: 'success'
                });

                // 重新加载列表
                this.loadArchivedCards();
              } else {
                wx.showToast({
                  title: res.result?.message || '恢复失败',
                  icon: 'error'
                });
              }
            },
            fail: (err: any) => {
              console.error('恢复卡片失败', err);
              wx.showToast({
                title: '恢复失败，请重试',
                icon: 'error'
              });
            },
            complete: () => {
              wx.hideLoading();
            }
          });
        }
      }
    });
  },

  /**
   * 确认删除卡片
   */
  confirmDeleteCard(cardId: string) {
    wx.showModal({
      title: '确认删除',
      content: '确定要永久删除这张卡片吗？此操作不可恢复！',
      confirmColor: '#e34d59',
      success: (res) => {
        if (res.confirm) {
          this.deleteCard(cardId);
        }
      }
    });
  },

  /**
   * 删除卡片
   */
  deleteCard(cardId: string) {
    wx.showLoading({ title: '删除中' });

    wx.cloud.callFunction({
      name: 'deleteCard',
      data: { cardId },
      success: (res: any) => {
        console.log('删除卡片成功', res);

        if (res.result && res.result.success) {
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });

          // 重新加载列表
          this.loadArchivedCards();
        } else {
          wx.showToast({
            title: res.result?.message || '删除失败',
            icon: 'error'
          });
        }
      },
      fail: (err: any) => {
        console.error('删除卡片失败', err);
        wx.showToast({
          title: '删除失败，请重试',
          icon: 'error'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 格式化日期
   */
  formatDate(date: any): string {
    if (!date) return '';
    
    const d = new Date(date);
    const month = d.getMonth() + 1;
    const day = d.getDate();
    
    return `${month}月${day}日`;
  },

  /**
   * 截取内容摘要
   */
  truncateContent(content: string): string {
    if (!content) return '';
    return content.length > 50 ? content.substring(0, 50) + '...' : content;
  }
});
