// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command
const MAX_LIMIT = 100

/**
 * 获取卡片列表云函数
 * @param {Object} event - 云函数调用参数
 * @param {string} [event.tagId] - 标签ID，可选，用于按标签筛选
 * @param {boolean} [event.archived] - 是否获取归档卡片，默认为false
 * @param {number} [event.page=1] - 页码，默认为1
 * @param {number} [event.pageSize=20] - 每页数量，默认为20
 * @param {Object} context - 云函数上下文
 * @returns {Object} 卡片列表和总数
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  // 获取参数
  const tagId = event.tagId || null
  const archived = event.archived || false
  const page = event.page || 1
  const pageSize = event.pageSize || 20
  const skip = (page - 1) * pageSize

  try {
    // 构建查询条件
    const query = {
      _openid: openid
    }

    // 根据归档状态筛选
    if (archived) {
      query.archived = true
    } else {
      // 默认只获取未归档的卡片
      query.archived = _.neq(true)
    }

    // 如果有标签ID，添加标签筛选条件
    if (tagId) {
      query.tagIds = _.in([tagId])
    }

    // 查询卡片总数
    const countResult = await db.collection('cards').where(query).count()
    const total = countResult.total

    // 查询卡片列表
    const cardsResult = await db.collection('cards')
      .where(query)
      .skip(skip)
      .limit(pageSize)
      .orderBy('createTime', 'desc')
      .get()

    // 获取所有标签，用于填充卡片的标签信息
    const tagsResult = await db.collection('tags')
      .where({
        _openid: openid
      })
      .get()

    const tags = tagsResult.data

    // 为卡片添加标签信息
    const cards = cardsResult.data.map(card => {
      const cardTags = []
      if (card.tagIds && card.tagIds.length > 0) {
        card.tagIds.forEach(tagId => {
          const tag = tags.find(t => t._id === tagId)
          if (tag) {
            cardTags.push({
              id: tag._id,
              name: tag.name,
              color: tag.color || '#0052d9'
            })
          }
        })
      }

      return {
        ...card,
        tags: cardTags
      }
    })

    return {
      success: true,
      data: cards,
      total: total,
      page: page,
      pageSize: pageSize
    }
  } catch (error) {
    console.error('获取卡片列表失败', error)
    return {
      success: false,
      error: error
    }
  }
}
