<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提醒设置页</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- TDesign风格的自定义配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0052d9', // TDesign主色调
                        success: '#00a870',
                        warning: '#ed7b2f',
                        error: '#e34d59',
                        'gray-1': '#f3f3f3',
                        'gray-2': '#eee',
                        'gray-3': '#e7e7e7',
                        'gray-4': '#dcdcdc',
                        'gray-5': '#c5c5c5',
                        'gray-6': '#a6a6a6',
                        'gray-7': '#8b8b8b',
                        'gray-8': '#5e5e5e',
                        'gray-9': '#3f3f3f',
                        'gray-10': '#2c2c2c',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #fff;
        }
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 56px;
            background-color: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
            z-index: 100;
        }
        .reminder-container {
            margin-top: 66px;
            padding: 20px 16px;
            padding-bottom: 80px;
        }
        .form-group {
            margin-bottom: 24px;
        }
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #5e5e5e;
        }
        .date-picker, .time-picker {
            width: 100%;
            padding: 12px;
            border: 1px solid #dcdcdc;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
            background-color: #fff;
        }
        .date-picker:focus, .time-picker:focus {
            border-color: #0052d9;
            outline: none;
        }
        .btn-primary {
            width: 100%;
            height: 48px;
            background-color: #0052d9;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-bottom: 16px;
        }
        .btn-primary:hover {
            background-color: #0045b6;
        }
        .btn-danger {
            width: 100%;
            height: 48px;
            background-color: #fff;
            color: #e34d59;
            border: 1px solid #e34d59;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btn-danger:hover {
            background-color: #ffecee;
        }
        .card-preview {
            padding: 16px;
            background-color: #f5f7fa;
            border-radius: 8px;
            margin-bottom: 24px;
        }
        .card-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }
        .card-content {
            font-size: 14px;
            color: #5e5e5e;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header">
        <button class="flex items-center text-gray-8">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m15 18-6-6 6-6"/>
            </svg>
            <span class="ml-1">返回</span>
        </button>
        <div class="text-lg font-medium">提醒设置</div>
        <div class="w-8 h-8"></div> <!-- 占位元素，保持标题居中 -->
    </div>
    
    <!-- 提醒设置容器 -->
    <div class="reminder-container">
        <!-- 卡片预览 -->
        <div class="card-preview">
            <div class="card-title">英语单词记忆技巧</div>
            <div class="card-content">通过词根词缀记忆法可以有效地扩大词汇量，例如"con-"表示"共同"，"-duct"表示"引导"，所以"conduct"就是"共同引导"...</div>
        </div>
        
        <!-- 日期选择 -->
        <div class="form-group">
            <label class="form-label">提醒日期</label>
            <input type="date" class="date-picker" value="2025-05-27">
        </div>
        
        <!-- 时间选择 -->
        <div class="form-group">
            <label class="form-label">提醒时间</label>
            <input type="time" class="time-picker" value="20:00">
        </div>
        
        <!-- 提醒说明 -->
        <div class="text-sm text-gray-6 mb-6">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="inline-block mr-1">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
            设置提醒后，微信将在指定时间向您推送复习通知
        </div>
        
        <!-- 按钮区域 -->
        <button class="btn-primary">设置提醒</button>
        <button class="btn-danger">取消提醒</button>
    </div>
    
    <script>
        // 设置默认日期为当前日期
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const dateInput = document.querySelector('.date-picker');
            
            // 如果没有预设值，则设置为今天
            if (!dateInput.value) {
                const year = today.getFullYear();
                let month = today.getMonth() + 1;
                let day = today.getDate();
                
                // 格式化月份和日期（补0）
                month = month < 10 ? '0' + month : month;
                day = day < 10 ? '0' + day : day;
                
                dateInput.value = `${year}-${month}-${day}`;
            }
            
            // 设置默认时间为当前时间
            const timeInput = document.querySelector('.time-picker');
            if (!timeInput.value) {
                let hours = today.getHours();
                let minutes = today.getMinutes();
                
                // 格式化小时和分钟（补0）
                hours = hours < 10 ? '0' + hours : hours;
                minutes = minutes < 10 ? '0' + minutes : minutes;
                
                timeInput.value = `${hours}:${minutes}`;
            }
        });
        
        // 设置提醒按钮点击事件
        document.querySelector('.btn-primary').addEventListener('click', function() {
            const dateInput = document.querySelector('.date-picker').value;
            const timeInput = document.querySelector('.time-picker').value;
            
            if (dateInput && timeInput) {
                alert(`提醒已设置为 ${dateInput} ${timeInput}`);
                // 这里可以添加实际保存提醒的逻辑
                
                // 返回上一页
                setTimeout(() => {
                    window.history.back();
                }, 1000);
            } else {
                alert('请选择提醒日期和时间');
            }
        });
        
        // 取消提醒按钮点击事件
        document.querySelector('.btn-danger').addEventListener('click', function() {
            if (confirm('确定要取消提醒吗？')) {
                alert('提醒已取消');
                // 这里可以添加实际取消提醒的逻辑
                
                // 返回上一页
                setTimeout(() => {
                    window.history.back();
                }, 1000);
            }
        });
    </script>
</body>
</html>
