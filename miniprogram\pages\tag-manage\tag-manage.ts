/**
 * 标签管理页面逻辑
 * 实现标签的添加、编辑和删除功能
 */
Page({
  data: {
    tags: [] as any[],
    newTagName: '',
    editTagName: '',
    editTagId: '',
    editDialogVisible: false,
    addingTag: false,
    isSelectionMode: false, // 是否为选择模式
    selectedTags: [] as string[], // 已选择的标签ID列表
    actionSheetVisible: false, // 操作菜单是否可见
    currentTagId: '', // 当前操作的标签ID
    currentTagName: '' // 当前操作的标签名称
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.loadTags();
  },

  /**
   * 加载标签列表
   */
  loadTags() {
    wx.showLoading({ title: '加载中' });

    wx.cloud.callFunction({
      name: 'getTags',
      success: (res: any) => {
        console.log('获取标签成功', res);

        if (res.result && res.result.success) {
          const tags = res.result.data || [];

          this.setData({
            tags: tags.map((tag: any) => ({
              id: tag._id,
              name: tag.name
            }))
          });
        } else {
          console.error('获取标签失败', res.result);
          this.setData({
            tags: []
          });
        }
      },
      fail: (err: any) => {
        console.error('获取标签失败', err);
        wx.showToast({
          title: '获取标签失败',
          icon: 'error'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 处理输入框变化事件
   */
  onInputChange(e: any) {
    this.setData({
      newTagName: e.detail.value
    });
  },

  /**
   * 处理编辑输入框变化事件
   */
  onEditInputChange(e: any) {
    this.setData({
      editTagName: e.detail.value
    });
  },

  /**
   * 处理添加标签按钮点击事件
   */
  onAddTag() {
    const tagName = this.data.newTagName.trim();

    if (!tagName) {
      wx.showToast({
        title: '请输入标签名称',
        icon: 'error'
      });
      return;
    }

    // 检查是否已存在同名标签
    const existingTag = this.data.tags.find(tag => tag.name === tagName);
    if (existingTag) {
      wx.showToast({
        title: '标签名称已存在',
        icon: 'error'
      });
      return;
    }

    this.setData({ addingTag: true });

    wx.cloud.callFunction({
      name: 'saveTag',
      data: { name: tagName },
      success: (res: any) => {
        console.log('创建标签成功', res);

        if (res.result && res.result.success) {
          // 清空输入框
          this.setData({
            newTagName: ''
          });

          // 重新加载标签列表
          this.loadTags();

          wx.showToast({
            title: '添加成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: res.result?.message || '添加失败',
            icon: 'error'
          });
        }
      },
      fail: (err: any) => {
        console.error('创建标签失败', err);
        wx.showToast({
          title: '添加失败，请重试',
          icon: 'error'
        });
      },
      complete: () => {
        this.setData({ addingTag: false });
      }
    });
  },

  /**
   * 处理标签长按事件
   */
  onTagLongPress(e: any) {
    const { id, name } = e.currentTarget.dataset;

    this.setData({
      currentTagId: id,
      currentTagName: name
    });

    // 显示操作菜单
    wx.showActionSheet({
      itemList: ['重命名', '删除'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 重命名标签
          this.setData({
            editTagId: id,
            editTagName: name,
            editDialogVisible: true
          });
        } else if (res.tapIndex === 1) {
          // 删除标签
          this.confirmDeleteTag(id, name);
        }
      }
    });
  },

  /**
   * 处理标签点击事件（选择模式下）
   */
  onTagTap(e: any) {
    if (!this.data.isSelectionMode) {
      return;
    }

    const { id } = e.currentTarget.dataset;
    const selectedTags = [...this.data.selectedTags];
    const index = selectedTags.indexOf(id);

    if (index > -1) {
      // 取消选择
      selectedTags.splice(index, 1);
    } else {
      // 选择
      selectedTags.push(id);
    }

    this.setData({
      selectedTags
    });
  },

  /**
   * 进入选择模式
   */
  enterSelectionMode() {
    this.setData({
      isSelectionMode: true,
      selectedTags: []
    });
  },

  /**
   * 退出选择模式
   */
  exitSelectionMode() {
    this.setData({
      isSelectionMode: false,
      selectedTags: []
    });
  },

  /**
   * 批量删除标签
   */
  batchDeleteTags() {
    if (this.data.selectedTags.length === 0) {
      wx.showToast({
        title: '请选择要删除的标签',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的${this.data.selectedTags.length}个标签吗？删除后，已使用这些标签的卡片将不再显示这些标签。`,
      confirmColor: '#e34d59',
      success: (res) => {
        if (res.confirm) {
          this.deleteBatchTags();
        }
      }
    });
  },

  /**
   * 执行批量删除
   */
  deleteBatchTags() {
    wx.showLoading({ title: '删除中' });

    const deletePromises = this.data.selectedTags.map(tagId => {
      return new Promise((resolve, reject) => {
        wx.cloud.callFunction({
          name: 'deleteTag',
          data: { tagId },
          success: resolve,
          fail: reject
        });
      });
    });

    Promise.all(deletePromises)
      .then(() => {
        wx.showToast({
          title: '批量删除成功',
          icon: 'success'
        });

        // 退出选择模式并重新加载
        this.exitSelectionMode();
        this.loadTags();
      })
      .catch((error) => {
        console.error('批量删除失败', error);
        wx.showToast({
          title: '删除失败，请重试',
          icon: 'error'
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  /**
   * 确认删除标签
   */
  confirmDeleteTag(tagId: string, tagName: string) {
    wx.showModal({
      title: '确认删除',
      content: `确定要删除标签"${tagName}"吗？删除后，已使用该标签的卡片将不再显示此标签。`,
      confirmColor: '#e34d59',
      success: (res) => {
        if (res.confirm) {
          this.deleteTag(tagId);
        }
      }
    });
  },

  /**
   * 删除标签
   */
  deleteTag(tagId: string) {
    wx.showLoading({ title: '删除中' });

    wx.cloud.callFunction({
      name: 'deleteTag',
      data: { tagId },
      success: (res: any) => {
        console.log('删除标签成功', res);

        if (res.result && res.result.success) {
          // 重新加载标签列表
          this.loadTags();

          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: res.result?.message || '删除失败',
            icon: 'error'
          });
        }
      },
      fail: (err: any) => {
        console.error('删除标签失败', err);
        wx.showToast({
          title: '删除失败，请重试',
          icon: 'error'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 处理编辑确认事件
   */
  onEditConfirm() {
    const tagName = this.data.editTagName.trim();

    if (!tagName) {
      wx.showToast({
        title: '请输入标签名称',
        icon: 'error'
      });
      return;
    }

    // 检查是否已存在同名标签（排除当前编辑的标签）
    const existingTag = this.data.tags.find(tag => tag.name === tagName && tag.id !== this.data.editTagId);
    if (existingTag) {
      wx.showToast({
        title: '标签名称已存在',
        icon: 'error'
      });
      return;
    }

    wx.showLoading({ title: '保存中' });

    wx.cloud.callFunction({
      name: 'saveTag',
      data: {
        tagId: this.data.editTagId,
        name: tagName
      },
      success: (res: any) => {
        console.log('更新标签成功', res);

        if (res.result && res.result.success) {
          // 重新加载标签列表
          this.loadTags();

          // 关闭编辑弹窗
          this.setData({
            editDialogVisible: false,
            editTagId: '',
            editTagName: ''
          });

          wx.showToast({
            title: '保存成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: res.result?.message || '保存失败',
            icon: 'error'
          });
        }
      },
      fail: (err: any) => {
        console.error('更新标签失败', err);
        wx.showToast({
          title: '保存失败，请重试',
          icon: 'error'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 处理编辑取消事件
   */
  onEditCancel() {
    this.setData({
      editDialogVisible: false,
      editTagId: '',
      editTagName: ''
    });
  }
});
