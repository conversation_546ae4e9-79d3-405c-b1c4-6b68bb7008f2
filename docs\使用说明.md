# 记忆卡片小程序使用说明

## 项目概述

记忆卡片小程序是一款基于微信小程序的记忆辅助工具，帮助用户创建、管理和复习记忆卡片。支持标签分类、图片上传、艾宾浩斯遗忘曲线提醒等功能。

## 主要功能

### 1. 用户登录
- 支持微信一键登录
- 自动保存登录状态
- 支持退出登录

### 2. 卡片管理
- **创建卡片**：支持添加标题、内容、图片和标签
- **编辑卡片**：可修改已有卡片的所有信息
- **删除卡片**：支持删除不需要的卡片
- **查看卡片**：列表展示所有卡片，支持按标签筛选

### 3. 标签管理
- **创建标签**：自定义标签名称
- **编辑标签**：修改标签名称
- **删除标签**：删除不需要的标签（会自动从相关卡片中移除）
- **标签筛选**：在卡片列表页按标签筛选卡片

### 4. 提醒功能
- **单次提醒**：为卡片设置特定时间的提醒
- **艾宾浩斯遗忘曲线提醒**：基于科学的遗忘曲线设置多次复习提醒
- **提醒管理**：查看和管理所有提醒

### 5. 图片上传
- 支持为卡片添加图片
- 自动上传到云存储
- 支持图片预览和删除

## 使用流程

### 首次使用
1. 打开小程序
2. 点击"微信一键登录"
3. 授权登录后进入卡片列表页

### 创建卡片
1. 在卡片列表页点击右下角的"+"按钮
2. 填写卡片标题（必填）
3. 填写卡片内容
4. 可选择上传图片
5. 可选择添加标签
6. 点击"保存"按钮

### 设置艾宾浩斯提醒
1. 在卡片编辑页面点击"艾宾浩斯遗忘曲线提醒"
2. 进入艾宾浩斯设置页面
3. 选择需要的复习时间点
4. 点击"保存设置"

### 管理标签
1. 在个人资料页面点击"标签管理"
2. 在标签管理页面可以：
   - 添加新标签
   - 编辑现有标签
   - 删除不需要的标签

### 复习提醒
1. 系统会根据艾宾浩斯遗忘曲线自动计算每张卡片的复习时间
2. 当卡片到达复习时间时，系统会自动发送微信订阅消息提醒
3. 点击提醒消息可直接跳转到对应卡片的复习页面
4. 提醒消息包含：
   - 复习时间
   - 学习计划标题
   - 具体需要复习的卡片名称

## 技术特性

### 前端技术
- 微信小程序原生开发
- TypeScript 类型支持
- TDesign 组件库
- 响应式设计

### 后端技术
- 微信云开发
- 云函数处理业务逻辑
- 云数据库存储数据
- 云存储管理文件

### 数据安全
- 基于微信 OpenID 的用户身份识别
- 数据隔离，用户只能访问自己的数据
- 云端存储，数据安全可靠

## 注意事项

### 订阅消息
- 首次登录时会提示用户开启消息推送功能
- 已配置订阅消息模板ID：lNNqgUJU3ITeDC9hlQosOO-a_bF4iF8EM_TpTF3Ap4c
- 系统会实时检测卡片复习时间，当卡片到达复习时间时立即发送提醒
- 每张卡片只会在到达复习时间时发送一次提醒，避免重复打扰
- 用户可以随时在微信设置中管理订阅消息权限

### 图片上传
- 支持 JPG、PNG 等常见图片格式
- 图片会自动压缩并上传到云存储
- 建议图片大小不超过 2MB

### 数据同步
- 所有数据实时同步到云端
- 支持多设备数据同步
- 网络异常时会显示相应提示

## 常见问题

### Q: 如何删除卡片？
A: 在卡片编辑页面底部有"删除卡片"按钮，点击后确认即可删除。

### Q: 删除标签会影响卡片吗？
A: 删除标签会自动从相关卡片中移除该标签，但不会删除卡片本身。

### Q: 艾宾浩斯提醒如何工作？
A: 根据艾宾浩斯遗忘曲线，在科学的时间点（如5分钟、30分钟、12小时、1天等）提醒用户复习。

### Q: 可以修改已设置的提醒吗？
A: 可以在提醒设置页面修改或取消已设置的提醒。

### Q: 数据会丢失吗？
A: 所有数据都保存在微信云开发平台，除非主动删除，否则不会丢失。

## 版本信息

- 当前版本：1.0.0
- 更新日期：2025年6月
- 开发者：张天颖

## 联系方式

如有问题或建议，请通过小程序内的反馈功能联系我们。
