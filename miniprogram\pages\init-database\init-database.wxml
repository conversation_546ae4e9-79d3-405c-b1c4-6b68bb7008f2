<view class="container">
  <view class="header">
    <text class="title">数据库初始化</text>
    <text class="subtitle">创建必要的数据库集合</text>
  </view>

  <view class="content">
    <t-button 
      theme="primary" 
      size="large" 
      bind:tap="onInitDatabase"
      loading="{{loading}}"
      disabled="{{loading}}"
    >
      {{loading ? '初始化中...' : '开始初始化数据库'}}
    </t-button>

    <view class="result" wx:if="{{result}}">
      <view class="result-header">
        <text class="result-title">初始化结果</text>
        <text class="result-status {{result.success ? 'success' : 'error'}}">
          {{result.success ? '成功' : '失败'}}
        </text>
      </view>
      
      <view class="result-message">{{result.message}}</view>
      
      <view class="result-details" wx:if="{{result.data && result.data.results}}">
        <text class="details-title">详细信息：</text>
        <view class="detail-item" wx:for="{{result.data.results}}" wx:key="collection">
          <text class="collection-name">{{item.collection}}</text>
          <text class="collection-status {{item.status}}">{{item.status}}</text>
          <text class="collection-message">{{item.message}}</text>
        </view>
      </view>
    </view>

    <view class="tips">
      <text class="tips-title">说明：</text>
      <text class="tips-content">此功能会自动创建提醒系统所需的数据库集合。初始化完成后，卡片的复习提醒功能将正常工作。</text>
    </view>
  </view>
</view>
