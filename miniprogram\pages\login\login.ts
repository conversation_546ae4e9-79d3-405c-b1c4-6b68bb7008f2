/**
 * 登录页面逻辑
 * 处理用户微信一键登录
 */
Page({
  data: {
    loading: false
  },

  /**
   * 处理登录按钮点击事件
   */
  onLogin() {
    this.setData({ loading: true });

    // 调用微信登录接口
    wx.login({
      success: (res) => {
        if (res.code) {
          // 获取到登录凭证后，调用云函数进行登录验证
          wx.cloud.callFunction({
            name: 'login',
            data: { code: res.code },
            success: (result: any) => {
              console.log('登录成功', result);

              // 保存用户信息到本地存储
              if (result.result && result.result.success) {
                const loginData = result.result.data;

                // 构建用户信息对象
                const userInfo = {
                  openid: loginData.openid,
                  unionid: loginData.unionid,
                  appid: loginData.appid,
                  nickName: loginData.userData?.nickName || '微信用户',
                  avatarUrl: loginData.userData?.avatarUrl || '',
                  _id: loginData.userData?._id,
                  isFirstLogin: loginData.userData?.isFirstLogin || false
                };

                wx.setStorage({
                  key: 'userInfo',
                  data: userInfo,
                  success: () => {
                    // 更新全局数据
                    const app = getApp<IAppOption>();
                    app.globalData.userInfo = userInfo;
                    app.globalData.isLoggedIn = true;

                    wx.showToast({
                      title: '登录成功',
                      icon: 'success'
                    });

                    // 如果是首次登录，请求订阅消息授权
                    if (userInfo.isFirstLogin) {
                      setTimeout(() => {
                        this.requestSubscribeMessage(() => {
                          wx.redirectTo({
                            url: '/pages/card-list/card-list'
                          });
                        });
                      }, 1500);
                    } else {
                      // 登录成功后跳转到卡片列表页
                      setTimeout(() => {
                        wx.redirectTo({
                          url: '/pages/card-list/card-list'
                        });
                      }, 1500);
                    }
                  },
                  fail: (err) => {
                    console.error('保存用户信息失败', err);
                    wx.showToast({
                      title: '登录失败，请重试',
                      icon: 'error'
                    });
                  }
                });
              } else {
                wx.showToast({
                  title: '登录失败，请重试',
                  icon: 'error'
                });
              }
            },
            fail: (err: any) => {
              console.error('登录失败', err);
              wx.showToast({
                title: '登录失败，请重试',
                icon: 'error'
              });
            },
            complete: () => {
              this.setData({ loading: false });
            }
          });
        } else {
          console.error('登录失败', res);
          this.setData({ loading: false });
          wx.showToast({
            title: '登录失败，请重试',
            icon: 'error'
          });
        }
      },
      fail: (err) => {
        console.error('微信登录接口调用失败', err);
        this.setData({ loading: false });
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 请求订阅消息授权（首次登录时）
   */
  requestSubscribeMessage(callback: Function) {
    const templateId = 'lNNqgUJU3ITeDC9hlQosOO-a_bF4iF8EM_TpTF3Ap4c'; // 用户提供的模板ID

    wx.showModal({
      title: '开启消息提醒',
      content: '为了及时提醒您复习卡片，建议开启消息推送功能。您可以随时在设置中关闭。',
      confirmText: '开启提醒',
      cancelText: '暂不开启',
      success: (res) => {
        if (res.confirm) {
          // 用户选择开启，请求订阅消息
          wx.requestSubscribeMessage({
            tmplIds: [templateId],
            success: (subscribeRes: any) => {
              if (subscribeRes[templateId] === 'accept') {
                wx.showToast({
                  title: '订阅成功！',
                  icon: 'success',
                  duration: 2000
                });
              }
            },
            fail: (err: any) => {
              console.error('订阅消息请求失败', err);
            },
            complete: () => {
              if (callback) callback();
            }
          });
        } else {
          // 用户选择暂不开启，直接执行回调
          if (callback) callback();
        }
      }
    });
  }
});
