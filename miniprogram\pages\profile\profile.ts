/**
 * 个人资料页面逻辑
 * 实现用户信息展示和设置、复习计划设置等功能
 */
Page({
  data: {
    userInfo: {
      nickName: '',
      avatarUrl: '',
      gender: 0 as 0 | 1 | 2,
      language: 'zh_CN' as 'en' | 'zh_CN' | 'zh_TW',
      city: '',
      province: '',
      country: ''
    },
    subscribeMessageEnabled: false // 订阅消息开启状态
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.loadUserInfo();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadUserInfo();
    this.loadSubscribeStatus();
    this.checkAuthStatus();
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    const app = getApp<IAppOption>();
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo
      });
    } else {
      // 尝试从本地存储获取
      wx.getStorage({
        key: 'userInfo',
        success: (res) => {
          this.setData({
            userInfo: res.data
          });
          app.globalData.userInfo = res.data;
        }
      });
    }
  },

  /**
   * 处理设置用户信息按钮点击事件
   */
  onSetUserInfo() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        const userInfo = res.userInfo;

        // 保存到本地存储和全局数据
        wx.setStorage({
          key: 'userInfo',
          data: userInfo
        });

        const app = getApp<IAppOption>();
        app.globalData.userInfo = userInfo;

        this.setData({
          userInfo: userInfo
        });

        wx.showToast({
          title: '设置成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('获取用户信息失败', err);
        wx.showToast({
          title: '获取信息失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 处理修改头像点击事件
   */
  onChangeAvatar() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];

        // 上传头像到云存储
        wx.showLoading({ title: '上传中' });

        const fileName = `avatar_${Date.now()}.jpg`;
        wx.cloud.uploadFile({
          cloudPath: `avatars/${fileName}`,
          filePath: tempFilePath,
          success: (uploadRes) => {
            const avatarUrl = uploadRes.fileID;

            // 调用云函数更新用户信息
            wx.cloud.callFunction({
              name: 'updateUserInfo',
              data: { avatarUrl },
              success: (res: any) => {
                if (res.result && res.result.success) {
                  // 更新用户信息
                  const userInfo = { ...this.data.userInfo, avatarUrl };

                  // 保存到本地存储和全局数据
                  wx.setStorage({
                    key: 'userInfo',
                    data: userInfo
                  });

                  const app = getApp<IAppOption>();
                  app.globalData.userInfo = userInfo;

                  this.setData({
                    userInfo: userInfo
                  });

                  wx.showToast({
                    title: '头像已更新',
                    icon: 'success'
                  });
                } else {
                  wx.showToast({
                    title: '头像更新失败',
                    icon: 'error'
                  });
                }
              },
              fail: (err: any) => {
                console.error('更新头像失败', err);
                wx.showToast({
                  title: '头像更新失败',
                  icon: 'error'
                });
              }
            });
          },
          fail: (err) => {
            console.error('上传头像失败', err);
            wx.showToast({
              title: '上传失败',
              icon: 'error'
            });
          },
          complete: () => {
            wx.hideLoading();
          }
        });
      }
    });
  },

  /**
   * 处理设置昵称事件
   */
  onSetNickName() {
    wx.showModal({
      title: '设置昵称',
      content: '请输入您的昵称',
      editable: true,
      placeholderText: this.data.userInfo.nickName || '请输入昵称',
      success: (res) => {
        if (res.confirm && res.content && res.content.trim()) {
          this.updateNickName(res.content.trim());
        }
      }
    });
  },

  /**
   * 更新昵称
   */
  updateNickName(nickName: string) {
    wx.showLoading({ title: '保存中' });

    // 调用云函数更新用户信息
    wx.cloud.callFunction({
      name: 'updateUserInfo',
      data: { nickName },
      success: (res: any) => {
        if (res.result && res.result.success) {
          // 更新用户信息
          const userInfo = { ...this.data.userInfo, nickName };

          // 保存到本地存储和全局数据
          wx.setStorage({
            key: 'userInfo',
            data: userInfo,
            success: () => {
              const app = getApp<IAppOption>();
              app.globalData.userInfo = userInfo;

              this.setData({
                userInfo: userInfo
              });

              wx.showToast({
                title: '昵称已更新',
                icon: 'success'
              });
            }
          });
        } else {
          wx.showToast({
            title: '昵称更新失败',
            icon: 'error'
          });
        }
      },
      fail: (err: any) => {
        console.error('更新昵称失败', err);
        wx.showToast({
          title: '保存失败',
          icon: 'error'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 加载订阅消息状态
   */
  loadSubscribeStatus() {
    // 从本地存储获取订阅状态
    wx.getStorage({
      key: 'subscribeMessageEnabled',
      success: (res) => {
        this.setData({
          subscribeMessageEnabled: res.data
        });
      },
      fail: () => {
        // 如果没有存储记录，默认为未开启
        this.setData({
          subscribeMessageEnabled: false
        });
      }
    });
  },

  /**
   * 保存订阅消息状态
   */
  saveSubscribeStatus(enabled: boolean) {
    // 保存到本地存储
    wx.setStorage({
      key: 'subscribeMessageEnabled',
      data: enabled
    });

    // 更新页面状态
    this.setData({
      subscribeMessageEnabled: enabled
    });

    // 保存到云端数据库
    wx.cloud.callFunction({
      name: 'updateSubscribeStatus',
      data: { enabled },
      success: (res: any) => {
        console.log('订阅状态保存到云端成功', res);
      },
      fail: (err: any) => {
        console.error('订阅状态保存到云端失败', err);
        // 即使云端保存失败，本地状态仍然有效
      }
    });
  },

  /**
   * 处理导航到艾宾浩斯遗忘曲线设置页面事件
   */
  onNavigateToEbbinghaus() {
    wx.navigateTo({
      url: '/pages/ebbinghaus/ebbinghaus'
    });
  },

  /**
   * 处理消息提醒设置事件
   */
  onRequestSubscribeMessage() {
    const currentStatus = this.data.subscribeMessageEnabled;

    if (currentStatus) {
      // 当前已开启，询问是否关闭
      wx.showModal({
        title: '消息提醒设置',
        content: '消息提醒当前已开启。您要关闭消息提醒吗？',
        confirmText: '关闭提醒',
        cancelText: '保持开启',
        confirmColor: '#e34d59',
        success: (res) => {
          if (res.confirm) {
            // 关闭消息提醒
            this.saveSubscribeStatus(false);
            wx.showToast({
              title: '消息提醒已关闭',
              icon: 'success',
              duration: 2000
            });
          }
        }
      });
    } else {
      // 当前未开启，询问是否开启
      wx.showModal({
        title: '开启消息提醒',
        content: '开启后，系统会在卡片到达复习时间时及时提醒您。',
        confirmText: '开启提醒',
        cancelText: '暂不开启',
        success: (res) => {
          if (res.confirm) {
            this.enableSubscribeMessage();
          }
        }
      });
    }
  },

  /**
   * 开启订阅消息
   */
  enableSubscribeMessage() {
    const templateId = 'lNNqgUJU3ITeDC9hlQosOO-a_bF4iF8EM_TpTF3Ap4c';

    // 请求订阅消息授权
    wx.requestSubscribeMessage({
      tmplIds: [templateId],
      success: (subscribeRes: any) => {
        console.log('订阅消息结果', subscribeRes);

        if (subscribeRes[templateId] === 'accept') {
          // 授权成功，保存状态
          this.saveSubscribeStatus(true);
          wx.showToast({
            title: '消息提醒已开启！',
            icon: 'success',
            duration: 2000
          });
        } else if (subscribeRes[templateId] === 'reject') {
          wx.showModal({
            title: '授权被拒绝',
            content: '您拒绝了消息推送授权。如需开启，请在微信设置中允许消息推送。',
            showCancel: false
          });
        } else if (subscribeRes[templateId] === 'ban') {
          wx.showModal({
            title: '消息推送被禁用',
            content: '消息推送功能被禁用，请在微信设置中开启后重试。',
            showCancel: false
          });
        } else {
          // 可能已经授权过了
          this.saveSubscribeStatus(true);
          wx.showToast({
            title: '消息提醒已开启！',
            icon: 'success',
            duration: 2000
          });
        }
      },
      fail: (err: any) => {
        console.error('订阅消息请求失败', err);
        wx.showModal({
          title: '授权失败',
          content: '订阅消息授权失败，请稍后重试。',
          showCancel: false
        });
      }
    });
  },

  /**
   * 测试订阅消息
   */
  onTestSubscribeMessage() {
    // 先检查是否已授权，如果没有授权则先请求授权
    wx.showModal({
      title: '测试消息提醒',
      content: '将发送一条测试消息到您的微信。如果您还没有授权订阅消息，请先点击"允许"。',
      confirmText: '开始测试',
      cancelText: '取消',
      success: (modalRes) => {
        if (modalRes.confirm) {
          // 先请求订阅消息授权
          const templateId = 'lNNqgUJU3ITeDC9hlQosOO-a_bF4iF8EM_TpTF3Ap4c';

          wx.requestSubscribeMessage({
            tmplIds: [templateId],
            success: (subscribeRes: any) => {
              console.log('订阅消息授权结果', subscribeRes);

              if (subscribeRes[templateId] === 'accept') {
                // 授权成功，发送测试消息
                this.sendTestMessage();
              } else if (subscribeRes[templateId] === 'reject') {
                wx.showModal({
                  title: '授权被拒绝',
                  content: '您拒绝了消息推送授权，无法发送测试消息。请在微信设置中开启后重试。',
                  showCancel: false
                });
              } else if (subscribeRes[templateId] === 'ban') {
                wx.showModal({
                  title: '消息推送被禁用',
                  content: '消息推送功能被禁用，请在微信设置中开启后重试。',
                  showCancel: false
                });
              } else {
                // 可能已经授权过了，直接尝试发送
                this.sendTestMessage();
              }
            },
            fail: (err: any) => {
              console.error('订阅消息授权失败', err);
              wx.showModal({
                title: '授权失败',
                content: '订阅消息授权失败，请稍后重试。',
                showCancel: false
              });
            }
          });
        }
      }
    });
  },

  /**
   * 检查授权状态
   */
  checkAuthStatus() {
    // 检查是否有授权过期的提示
    wx.getStorage({
      key: 'authExpiredWarning',
      success: (res) => {
        if (res.data && this.data.subscribeMessageEnabled) {
          // 有授权过期警告且当前显示为已开启，提示用户重新授权
          wx.showModal({
            title: '授权已过期',
            content: '您的消息提醒授权已过期，无法接收提醒。是否重新授权？',
            confirmText: '重新授权',
            cancelText: '稍后再说',
            success: (modalRes) => {
              if (modalRes.confirm) {
                this.enableSubscribeMessage();
              } else {
                this.saveSubscribeStatus(false);
              }
              // 清除警告标记
              wx.removeStorage({ key: 'authExpiredWarning' });
            }
          });
        }
      }
    });
  },

  /**
   * 发送测试消息
   */
  sendTestMessage() {
    wx.showLoading({ title: '发送测试消息中' });

    wx.cloud.callFunction({
      name: 'checkSubscribeStatus',
      success: (res: any) => {
        console.log('测试消息结果', res);

        if (res.result && res.result.success) {
          wx.showToast({
            title: '测试消息发送成功！请查看微信通知',
            icon: 'success',
            duration: 3000
          });

          // 更新本地订阅状态
          this.saveSubscribeStatus(true);
        } else {
          const errorCode = res.result?.errorCode;
          let content = res.result?.message || '发送失败';

          if (errorCode === 43101 || errorCode === 43104) {
            // 授权失效，引导用户重新授权
            wx.showModal({
              title: '需要重新授权',
              content: '订阅消息授权已失效，需要重新授权才能接收提醒。是否立即重新授权？',
              confirmText: '重新授权',
              cancelText: '稍后再说',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  this.enableSubscribeMessage();
                } else {
                  this.saveSubscribeStatus(false);
                }
              }
            });
            return;
          } else if (errorCode === 40037) {
            content = '订阅消息模板配置有误，请联系开发者。';
          }

          wx.showModal({
            title: '测试消息发送失败',
            content: content,
            showCancel: false
          });

          // 如果需要重新授权，更新本地状态
          if (res.result?.needReauth) {
            this.saveSubscribeStatus(false);
          }
        }
      },
      fail: (err: any) => {
        console.error('测试消息失败', err);
        wx.showModal({
          title: '测试失败',
          content: '网络错误，请稍后重试',
          showCancel: false
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 处理导航到归档卡片页面事件
   */
  onNavigateToArchivedCards() {
    wx.navigateTo({
      url: '/pages/archived-cards/archived-cards'
    });
  },

  /**
   * 处理导航到标签管理页面事件
   */
  onNavigateToTagManage() {
    wx.navigateTo({
      url: '/pages/tag-manage/tag-manage'
    });
  },

  /**
   * 处理导航到数据库初始化页面事件
   */
  onNavigateToInitDatabase() {
    wx.navigateTo({
      url: '/pages/init-database/init-database'
    });
  },

  /**
   * 手动触发提醒处理
   */
  onTriggerReminders() {
    wx.showLoading({ title: '检查提醒中...' });

    wx.cloud.callFunction({
      name: 'triggerReminders',
      success: (res: any) => {
        console.log('手动触发提醒结果', res);

        if (res.result && res.result.success) {
          const data = res.result.data;
          const totalReminders = data?.data?.totalReminders || 0;
          const results = data?.data?.results || [];

          let message = `检查完成！找到 ${totalReminders} 条提醒记录`;
          if (results.length > 0) {
            const sentCount = results.filter((r: any) => r.status === 'sent').length;
            if (sentCount > 0) {
              message += `，已发送 ${sentCount} 条提醒`;
            }
          }

          wx.showToast({
            title: message,
            icon: 'success',
            duration: 3000
          });
        } else {
          wx.showModal({
            title: '触发失败',
            content: res.result?.message || '未知错误',
            showCancel: false
          });
        }
      },
      fail: (err: any) => {
        console.error('手动触发提醒失败', err);
        wx.showModal({
          title: '触发失败',
          content: '网络错误，请稍后重试',
          showCancel: false
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 直接测试消息发送
   */
  onTestDirectMessage() {
    wx.showModal({
      title: '直接测试消息',
      content: '将直接发送一条测试消息到您的微信。如果您还没有授权订阅消息，请先点击"允许"。',
      confirmText: '开始测试',
      cancelText: '取消',
      success: (modalRes) => {
        if (modalRes.confirm) {
          // 先请求订阅消息授权
          const templateId = 'lNNqgUJU3ITeDC9hlQosOO-a_bF4iF8EM_TpTF3Ap4c';

          wx.requestSubscribeMessage({
            tmplIds: [templateId],
            success: (subscribeRes: any) => {
              console.log('直接测试消息授权结果', subscribeRes);

              if (subscribeRes[templateId] === 'accept') {
                // 授权成功，发送测试消息
                this.sendDirectTestMessage();
              } else if (subscribeRes[templateId] === 'reject') {
                wx.showModal({
                  title: '授权被拒绝',
                  content: '您拒绝了消息推送授权，无法发送测试消息。请在微信设置中开启后重试。',
                  showCancel: false
                });
              } else if (subscribeRes[templateId] === 'ban') {
                wx.showModal({
                  title: '消息推送被禁用',
                  content: '消息推送功能被禁用，请在微信设置中开启后重试。',
                  showCancel: false
                });
              } else {
                // 可能已经授权过了，直接尝试发送
                this.sendDirectTestMessage();
              }
            },
            fail: (err: any) => {
              console.error('直接测试消息授权失败', err);
              wx.showModal({
                title: '授权失败',
                content: '订阅消息授权失败，请稍后重试。',
                showCancel: false
              });
            }
          });
        }
      }
    });
  },

  /**
   * 发送直接测试消息
   */
  sendDirectTestMessage() {
    wx.showLoading({ title: '发送测试消息中...' });

    wx.cloud.callFunction({
      name: 'triggerReminders',
      data: {
        testMessage: true
      },
      success: (res: any) => {
        console.log('直接测试消息结果', res);

        if (res.result && res.result.success) {
          wx.showToast({
            title: '测试消息已发送',
            icon: 'success',
            duration: 2000
          });
        } else {
          const result = res.result;
          let content = result?.message || '未知错误';

          // 如果是授权问题，提示用户重新授权
          if (result?.needReauth || result?.errorCode === 43101) {
            wx.showModal({
              title: '需要重新授权',
              content: '订阅消息授权已失效，需要重新授权才能接收提醒。是否立即重新授权？',
              confirmText: '重新授权',
              cancelText: '稍后再说',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  this.enableSubscribeMessage();
                }
              }
            });
            return;
          }

          wx.showModal({
            title: '发送失败',
            content: content,
            showCancel: false
          });
        }
      },
      fail: (err: any) => {
        console.error('直接测试消息失败', err);
        wx.showModal({
          title: '发送失败',
          content: '网络错误，请稍后重试',
          showCancel: false
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },



  /**
   * 处理显示关于信息事件
   */
  onShowAbout() {
    wx.showModal({
      title: '关于记忆卡片',
      content: '记忆卡片小程序是一款帮助用户高效记忆和复习知识点的工具，基于艾宾浩斯遗忘曲线原理，科学安排复习计划。\n\n版本：1.0.0',
      showCancel: false
    });
  },

  /**
   * 处理清除缓存事件
   */
  onClearCache() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除本地缓存吗？这不会删除您的卡片数据。',
      success: (res) => {
        if (res.confirm) {
          wx.clearStorage({
            success: () => {
              wx.showToast({
                title: '缓存已清除',
                icon: 'success'
              });

              // 重新加载页面
              this.loadUserInfo();
            }
          });
        }
      }
    });
  },

  /**
   * 处理退出登录事件
   */
  onLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      confirmColor: '#e34d59',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储的用户信息
          wx.removeStorage({
            key: 'userInfo'
          });

          // 清除全局数据
          const app = getApp<IAppOption>();
          app.globalData.userInfo = null;
          app.globalData.isLoggedIn = false;

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });

          // 跳转到登录页
          setTimeout(() => {
            wx.redirectTo({
              url: '/pages/login/login'
            });
          }, 1500);
        }
      }
    });
  }
});
