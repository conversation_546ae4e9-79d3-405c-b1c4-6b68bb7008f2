// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 归档卡片云函数
 * @param {Object} event - 云函数调用参数
 * @param {string} event.cardId - 卡片ID
 * @param {Object} context - 云函数上下文
 * @returns {Object} 归档结果
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { cardId } = event

  try {
    // 验证参数
    if (!cardId) {
      return {
        success: false,
        message: '卡片ID不能为空'
      }
    }

    // 检查卡片是否存在且属于当前用户
    const cardResult = await db.collection('cards').doc(cardId).get()
    
    if (!cardResult.data) {
      return {
        success: false,
        message: '卡片不存在'
      }
    }

    if (cardResult.data._openid !== wxContext.OPENID) {
      return {
        success: false,
        message: '无权限操作此卡片'
      }
    }

    // 更新卡片为归档状态
    const updateResult = await db.collection('cards').doc(cardId).update({
      data: {
        archived: true,
        archivedTime: db.serverDate(),
        updateTime: db.serverDate()
      }
    })

    console.log('归档卡片成功', updateResult)

    return {
      success: true,
      message: '卡片归档成功',
      data: {
        updated: updateResult.stats.updated
      }
    }
  } catch (error) {
    console.error('归档卡片失败', error)
    return {
      success: false,
      message: '归档失败',
      error: error
    }
  }
}
