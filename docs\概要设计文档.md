# ＜记忆卡片＞概要设计与详细设计

**作者：** 张天颖 [cite: 1]
**学号：** 0221147019 [cite: 1]
**完成日期：** 2024年12月

## 目录

* [概要设计与详细设计](#概要设计与详细设计)
* [一、体系结构设计](#一体系结构设计)
    * [1. 软件功能概要](#1-软件功能概要)
    * [2. 系统功能结构图](#2-系统功能结构图)
    * [3. 系统体系架构示意图](#3-系统体系架构示意图)
* [二、接口设计](#二接口设计)
    * [2.1 人机交互界面接口](#21-人机交互界面接口)
        * [2.1.1 登录页面（微信一键登录）](#211-登录页面微信一键登录)
        * [2.1.2 卡片列表页（卡片摘要浏览与详情）](#212-卡片列表页卡片摘要浏览与详情)
        * [2.1.3 卡片编辑页（新建/编辑卡片）](#213-卡片编辑页新建编辑卡片)
        * [2.1.4 标签管理页（添加、修改、删除标签）](#214-标签管理页添加修改删除标签)
        * [2.1.5 艾宾浩斯设置页（复习计划配置）](#215-艾宾浩斯设置页复习计划配置)
        * [2.1.6 卡片复习页（复习界面与效果反馈）](#216-卡片复习页复习界面与效果反馈)
        * [2.1.7 归档卡片页（归档管理）](#217-归档卡片页归档管理)
    * [2.2 主要功能模块接口设计](#22-主要功能模块接口设计)
        * [2.2.1 登录模块](#221-登录模块)
        * [2.2.2 卡片模块](#222-卡片模块)
        * [2.2.3 标签模块](#223-标签模块)
        * [2.2.4 提醒模块](#224-提醒模块)
        * [2.2.5 归档模块](#225-归档模块)
* [三、数据设计](#三数据设计)
    * [3.1 数据库逻辑结构设计](#31-数据库逻辑结构设计)
    * [3.2 数据库物理结构设计](#32-数据库物理结构设计)
    * [3.3 数据建模图示](#33-数据建模图示)
* [四、过程设计](#四过程设计)
    * [4.1 用户登录流程](#41-用户登录流程)
    * [4.2 创建记忆卡片流程](#42-创建记忆卡片流程)
    * [4.3 编辑与保存卡片流程](#43-编辑与保存卡片流程)
    * [4.4 删除卡片流程](#44-删除卡片流程)
    * [4.5 标签添加与管理流程](#45-标签添加与管理流程)
    * [4.6 提醒设置与取消流程](#46-提醒设置与取消流程)

---

## 一、体系结构设计

### 1. 软件功能概要

记忆卡片小程序围绕用户个人的记忆管理需求，提供了一系列核心功能模块，帮助用户创建、分类和巩固记忆内容 [cite: 1]。总体上，小程序实现的主要功能如下：

* **卡片管理**：支持用户新建记忆卡片以记录知识点或事项，每张卡片包含标题和内容（支持富文本和插入图片）[cite: 1]。用户可编辑更新卡片内容，删除不需要的卡片，并通过列表视图浏览所有卡片概要，点击查看详情 [cite: 1]。该模块确保增删改查操作简便，高效管理卡片信息 [cite: 1]。
* **标签管理**：支持用户为卡片添加自定义标签，以对卡片分类归组 [cite: 1]。用户可以新建、编辑、删除标签，并在创建或编辑卡片时指定一个或多个标签 [cite: 1]。通过标签筛选功能，用户能够按类别查看相关卡片集合，实现对不同主题记忆内容的分类管理，方便快速定位特定卡片 [cite: 1]。
* **用户登录与身份管理**：采用微信一键登录机制，用户可直接使用微信账户授权免注册登录 [cite: 1]。登录后系统获取微信提供的唯一标识OpenID用于区分用户，实现数据隔离 [cite: 1]。小程序利用微信平台维护用户会话状态，无需每次重复登录 [cite: 1]。用户登录成功后，其记忆卡片和标签等数据将专人专用，确保不同用户的数据互不干扰 [cite: 1]。
* **数据存储与云同步**：小程序使用微信小程序云开发的数据库存储用户数据，包括卡片内容、标签分类等，实现云端持久化 [cite: 1]。用户在本地对卡片或标签的新增、修改、删除操作通过云函数实时同步到云数据库，保证多设备访问时数据的一致性 [cite: 1]。系统支持一定的离线缓存，确保用户在无网络时也能浏览已保存的卡片，并在网络恢复后自动同步离线期间的更新操作 [cite: 1]。
* **复习提醒**：提供基于艾宾浩斯遗忘曲线的智能复习提醒功能 [cite: 1]。系统自动为每张卡片安排多轮复习时间点（5分钟、30分钟、12小时、1天、2天、7天、30天等），用户可在全局设置中自定义启用的复习时间点 [cite: 1]。小程序通过微信订阅消息服务，在卡片到达复习时间时立即发送提醒通知 [cite: 1]。用户复习时可选择记忆效果（完全记住、部分记住、完全忘记），系统根据反馈调整下次复习时间 [cite: 1]。
* **卡片归档**：当卡片完成所有复习轮次且用户选择"完全记住"时，系统自动将卡片归档 [cite: 1]。归档的卡片可在专门页面查看和管理，用户可选择恢复归档卡片重新开始复习计划 [cite: 1]。
* **批量操作**：支持批量删除卡片和标签，通过长按激活选择模式，提高管理效率 [cite: 1]。

以上模块相互配合，为用户打造一个完善的移动记忆库 [cite: 1]。用户通过小程序可以方便地创建并管理海量的记忆卡片，利用标签梳理知识结构，并借助云端同步和智能提醒，更有效地巩固所学内容 [cite: 1]。

### 2. 系统功能结构图

软件在总体设计阶段采用结构化设计方法，依据数据流分析结果绘制系统的模块结构图 [cite: 1]。如属变换型的数据流，首先根据数据流程确定输入、变换核心和输出三个区域，得到系统的初始结构，然后再进行模块重组优化 [cite: 1]。图1和图2分别展示了记忆卡片小程序的初始结构和优化后结构 [cite: 1]。

[Image: 初始结构图 - 记忆卡片系统分为登录处理、卡片管理、标签管理、提醒处理四个主要模块。] [cite: 2]

上述图 1初始结构图清晰地体现了系统主要功能的模块划分，但在模块独立性和可维护性方面有进一步优化空间 [cite: 1]。例如，卡片和标签模块各自直接进行数据存取，存在重复的数据库操作逻辑；登录和提醒模块都涉及与微信平台交互，亦存在冗余接口调用 [cite: 1]。为提高模块的内聚性、减少耦合，需对初始结构进行优化 [cite: 1]。

[Image: 优化后结构图 - 记忆卡片系统顶层模块下分登录处理、卡片管理、标签管理、提醒处理。登录处理依赖微信接口。卡片管理和标签管理依赖云数据库。提醒处理依赖微信通知服务。] [cite: 3]

经过上述优化，结构图展现出更合理的分层：顶层控制模块下分为业务功能层和基础服务层 [cite: 1]。业务功能层包括用户登录、卡片管理、标签管理、提醒功能等模块；基础服务层由微信平台接口和云数据库接口模块支撑，实现外部服务接入和数据存储 [cite: 1]。优化后的模块结构具有高内聚、低耦合的特点，为下一步的详细设计和实现打下基础 [cite: 1]。

### 3. 系统体系架构示意图

记忆卡片小程序基于微信小程序的前后端云一体化架构，实现客户端与云端服务的协同工作 [cite: 1]。图 3系统体系架构示意图给出了本系统的部署架构示意图，各组成部分及交互流程如下[cite: 1]:

[Image: 系统体系架构示意图 - 用户（微信客户端）通过安全API通信与小程序前端（界面呈现+本地逻辑）交互。小程序前端与云函数（业务逻辑处理）交互。云函数与云数据库（卡片/标签/提醒 持久化存储）交互。] [cite: 4]

微信平台在该架构中提供了重要的基础支持：首先，小程序利用微信登录服务进行用户身份认证 [cite: 1]。用户首次进入时，小程序前端调用微信登录接口获取临时凭证，经由微信服务器验证后返回用户的OpenID给云端，用于建立用户账户和数据隔离 [cite: 1]。然后，小程序使用微信订阅消息服务实现提醒通知 [cite: 1]。当用户设定了复习提醒，小程序向微信平台发出订阅请求并登记提醒内容；到达设定时间后，微信服务器将订阅消息推送到该用户的微信上，提醒其打开小程序进行复习 [cite: 1]。整个过程中，小程序借助微信生态提供的现成服务实现了免密登录和消息触达等功能 [cite: 1]。

综上，记忆卡片小程序的体系架构充分利用了微信小程序云开发的优势，将 **客户端**、**云函数后端**、**云数据库** 以及 **微信平台服务** 有机结合 [cite: 1]。一方面，前后端无缝衔接和数据云端存储保证了多终端下用户数据的一致与安全；另一方面，微信平台提供的登录和消息能力降低了开发门槛，提升了系统的用户体验和可靠性 [cite: 1]。在此架构下，小程序能够为用户提供随时随地、数据永不丢失的记忆卡片服务 [cite: 1]。

## 二、接口设计

### 2.1 人机交互界面接口

应用的主要界面包括登录页面、卡片列表页、卡片编辑页、标签管理页和提醒设置页 [cite: 1]。下面针对每个界面提供原型草图并说明其布局、交互方式和使用流程 [cite: 1]。

#### 2.1.1 登录页面（微信一键登录）

[Image: 登录页面 - 顶部为“记忆卡片小程序”标题，中间为“微信一键登录”按钮及提示文字“使用微信账号快速登录”，下方流程指向“微信授权机制获取用户身份”，再指向“主功能界面”。] [cite: 5]

图 4登录页面用于用户微信一键登录 [cite: 1]。界面布局简洁居中，顶部显示应用名称或Logo，中部突出一个绿色的”微信一键登录”按钮，下方用灰色文字提示可使用微信账号快速登录 [cite: 1]。用户点击该按钮后，小程序将调用微信提供的授权机制获取用户身份 [cite: 1]。登录过程对用户而言非常简便，无需额外输入信息，授权成功后会自动跳转进入主功能界面 [cite: 1]。

**用户操作说明：** [cite: 1]

* **打开小程序** – 用户首次进入小程序时，将看到登录页面，引导其进行微信授权登录 [cite: 1]。
* **一键授权** – 点击绿色的”微信一键登录”按钮，小程序会弹出微信授权提示，用户确认授权后完成登录 [cite: 1]。
* **进入应用** – 授权成功后，系统自动获取用户信息并跳转到卡片列表页，用户开始正常使用应用功能 [cite: 1]。

#### 2.1.2 卡片列表页（卡片摘要浏览与详情）

卡片列表页是应用的主界面，显示当前用户所有记忆卡片的摘要列表 [cite: 1]。页面顶部是标题栏”卡片列表”，左侧提供标签筛选按钮，右上角有”＋”按钮用于新建卡片 [cite: 1]。列表区域按卡片逐项列出，每个卡片项以卡片标题和内容摘要呈现，可能附有标签标记和创建日期等信息 [cite: 1]。界面布局清晰，用户可以垂直滚动浏览所有卡片摘要 [cite: 1]。

**用户操作说明：** [cite: 1]

* **浏览卡片** – 进入卡片列表页后，用户可以上下滚动查看已创建的记忆卡片 [cite: 1]。每个列表项显示卡片标题、部分内容（摘要）、关联标签标识（如”[标签]”）及日期等 [cite: 1]。
* **查看详情** – 点击任一卡片列表项，应用将进入该卡片的详情/编辑页面，显示完整内容供用户查看或编辑 [cite: 1]。
* **新建卡片** – 点击右上角”＋”新建按钮，跳转到卡片编辑页，用户可添加新的记忆卡片 [cite: 1]。
* **标签筛选** – 点击顶部标题栏左侧的筛选下拉按钮，将弹出标签列表供选择 [cite: 1]。用户选择某个标签后，列表将过滤只显示具有该标签的卡片，便于分类浏览（再次选择”全部”或取消筛选则恢复显示全部卡片）[cite: 1]。

#### 2.1.3 卡片编辑页（新建/编辑卡片）

卡片编辑页用于新建或修改记忆卡片的内容 [cite: 1]。界面包含多个输入区域：标题、内容、图片、标签和提醒设置 [cite: 1]。标题栏显示”编辑卡片”（新建时也使用该界面），左上角提供返回按钮 [cite: 1]。页面主要部分依次为： **标题输入框** （单行文本）， **内容输入区** （多行文本框用于详细内容，可以划线表示多行输入）， **图片上传区域** （方框占位显示当前图片或”上传”按钮，点击可选择图片插入卡片）， **标签选择区域** （显示已选标签的小灰块，并提供”＋”按钮添加标签），以及 **提醒设置** （显示当前提醒状态，”无”表示未设置，点击该区域可进入提醒设置页）[cite: 1]。页面底部提供醒目的”保存”按钮（蓝色）用于提交卡片更改 [cite: 1]。界面交互以表单填写为主，布局简洁方便用户逐项输入 [cite: 1]。

**用户操作说明：** [cite: 1]

* **填写标题和内容** – 用户在对应输入框中输入卡片的标题和正文内容 [cite: 1]。标题简要概括记忆点，内容支持多段文本（可选插入图片辅助记忆）[cite: 1]。
* **上传相关图片** – （可选）点击”图片”区域的占位方框，弹出图片选择界面，用户选取一张图片后将显示在卡片中（用于保存截图或示意图）[cite: 1]。
* **绑定标签** – 点击”标签”区域的”＋”按钮，弹出标签选择对话框或下拉菜单 [cite: 1]。用户可从已创建的标签中选择一个或多个绑定到该卡片；如需新标签则通过标签管理页添加后再选择 [cite: 1]。已选标签会在该区域以小块显示 [cite: 1]。
* **设置提醒** – 点击”提醒”区域（默认显示”无”或当前提醒时间），进入提醒设置页面，选择复习提醒的日期和时间 [cite: 1]。设置完毕保存返回后，提醒状态会更新显示在编辑页 [cite: 1]。若需要取消提醒，用户可在提醒设置页执行取消操作 [cite: 1]。
* **保存卡片** – 输入完成后，点击底部”保存”按钮 [cite: 1]。系统将校验必填项（如标题、内容非空），然后保存卡片数据至云端 [cite: 1]。若是新建卡片则创建一条新记录，编辑卡片则更新原有内容 [cite: 1]。保存成功后，页面返回上一级（卡片列表页）并显示最新的卡片列表；如保存失败（例如网络异常），则提示错误信息供用户重试 [cite: 1]。

#### 2.1.4 标签管理页（添加、修改、删除标签）

标签管理页用于管理用户自定义的标签分类 [cite: 1]。界面顶部为”标签管理”标题，左上角提供返回按钮 [cite: 1]。主要区域包含两个部分： **新标签添加** 输入和 **已有标签列表** [cite: 1]。在顶部，提供一个文本输入框用于输入新标签名称，旁边有绿色”添加”按钮，点击后会将新标签保存并加入列表 [cite: 1]。下方列出了用户已经创建的标签，每行显示标签名称，并在右侧提供”改”和”删”两个操作按钮：点选”改”按钮可以对该标签重命名，点选”删”按钮则要求用户确认后删除该标签 [cite: 1]。各标签项之间用细线分隔以提高可读性 [cite: 1]。通过此界面，用户可以方便地增加新标签或维护现有标签，以便在卡片编辑时使用 [cite: 1]。

**用户操作说明：** [cite: 1]

* **添加标签** – 在页面顶部的输入框中输入新标签的名称，点击旁边”添加”按钮 [cite: 1]。系统检查名称有效且不重复后，将新标签保存到数据库，并即时更新下方标签列表显示 [cite: 1]。
* **修改标签** – 点击某个已有标签行右侧的”改”按钮 [cite: 1]。系统弹出重命名对话框（或直接在列表中切换为可编辑状态），用户输入新的标签名称并确认 [cite: 1]。保存成功后，列表中该标签名称更新，所有使用该标签的卡片将同步显示新名称 [cite: 1]。
* **删除标签** – 点击标签行右侧的”删”按钮 [cite: 1]。系统弹出确认删除提示，防止误操作 [cite: 1]。确认后，系统将从数据库中移除该标签，并在界面列表中删除该项 [cite: 1]。已删除标签可能会从相关卡片的标签字段中移除（卡片仍保留但不再绑定该标签）[cite: 1]。删除操作完成后界面刷新标签列表 [cite: 1]。

#### 2.1.5 提醒设置页（添加/取消定时提醒）

提醒设置页用于为记忆卡片添加或取消定时复习提醒 [cite: 1]。顶部标题为”提醒设置”，带返回按钮 [cite: 1]。界面中部允许用户选择提醒的日期和时间：包括 **提醒日期** （格式例如”2025-05-10”）和 **提醒时间** （例如”08:00”）两个字段，用户点击各字段可弹出日期选择器和时间选择器进行设定 [cite: 1]。选定时间后，点击下方蓝色”设置提醒”按钮保存设置 [cite: 1]。如果当前卡片已设置过提醒，页面下方将显示红色的”取消提醒”按钮，用户可点击以移除已有的提醒计划 [cite: 1]。

**用户操作说明：** [cite: 1]

* **进入提醒设置** – 从卡片编辑页点击”提醒”区域进入本页面 [cite: 1]。如果之前没有为该卡片设置提醒，日期和时间字段默认空白或当前日期时间；如果已有提醒，则会显示之前设定的日期和时间并提供取消选项 [cite: 1]。
* **选择日期和时间** – 点击”提醒日期”字段，弹出日期选择控件供用户选取目标复习日期；点击”提醒时间”字段，弹出时间选择控件选取具体时间点 [cite: 1]。用户选好日期和时间后，两项字段会显示所选值 [cite: 1]。
* **保存提醒** – 点击蓝色”设置提醒”按钮，系统将所选日期时间保存为该卡片的提醒计划，并通过微信订阅消息接口订阅在指定时间推送通知（需用户已授权消息通知）[cite: 1]。设置成功后，返回卡片编辑页时，”提醒”区域会显示已设定的时间 [cite: 1]。
* **取消提醒** – 若卡片已有提醒且用户希望删除，点击红色”取消提醒”按钮 [cite: 1]。系统将取消该卡片对应的提醒计划（从数据库或订阅列表中移除），返回时编辑页”提醒”显示为”无” [cite: 1]。取消操作需用户确认，以防误触 [cite: 1]。

### 2.2 主要功能模块接口设计

根据上述界面和功能，本小程序后端划分为登录模块、卡片模块、标签模块和提醒模块等主要功能模块 [cite: 1]。下面列出各模块提供的接口说明，包括接口名称、输入输出参数、调用方式及错误处理机制 [cite: 1]。

#### 2.2.1 登录模块

* **模块说明：** 提供用户身份认证功能，利用微信一键登录获取用户唯一标识，维护会话状态 [cite: 1]。
* **接口名称：** `login()`（微信登录接口封装）[cite: 1]
* **输入参数：** 无需显式输入参数 [cite: 1]。调用时小程序前端会自动传递微信分配的临时登录凭证（code）给后端 [cite: 1]。后端使用该凭证调用微信服务器验证 [cite: 1]。
* **输出参数：** [cite: 1]
    * `success` (Boolean)：登录是否成功 [cite: 1]。
    * `userId` (String)：用户标识ID（微信OpenID，用于标识用户身份）[cite: 1]。
    * `message` (String)：提示信息，如失败原因或成功状态描述 [cite: 1]。
* **调用方式：** 前端调用微信提供的登录API获取code后，通过云函数（或HTTPS接口）将code发送至后端登录接口进行校验 [cite: 1]。该调用为 **异步** 方式，登录接口在微信服务器验证完成后返回结果 [cite: 1]。调用源为小程序前端，在应用初始化或用户点击登录按钮时触发 [cite: 1]。
* **错误处理：** 若微信验证失败或网络异常，接口返回`success=false`，并在`message`中给出错误码及描述 [cite: 1]。例如：登录过期、网络错误等 [cite: 1]。前端收到失败响应后，会提示用户重新尝试登录或检查网络 [cite: 1]。登录接口本身不抛出未处理异常，所有错误转换为标准输出格式返回 [cite: 1]。

#### 2.2.2 卡片模块

* **模块说明：** 负责记忆卡片的数据管理，提供创建、查询、更新、删除等功能接口 [cite: 1]。每条卡片记录包含标题、内容、图片链接、关联标签、提醒设置等信息字段 [cite: 1]。
* **接口列表：** [cite: 1]
    1.  **CreateCard（创建卡片）** [cite: 1]
        * **接口名称：** `createCard(data)` [cite: 1]
        * **输入参数：** [cite: 1]
            * `title` (String)：卡片标题 [cite: 1]。
            * `content` (String)：卡片内容正文 [cite: 1]。
            * `imageUrl` (String)：图片文件的URL或路径（可选，没有图片则为空）[cite: 1]。
            * `tags` (Array<String>)：关联的标签ID列表（可为空列表）[cite: 1]。
            * `reminderTime` (Datetime)：提醒时间（可选，没有设置提醒则为空）[cite: 1]。
            * `userId` (String)：用户ID（标识卡片归属用户）[cite: 1]。
        * **输出参数：** [cite: 1]
            * `success` (Boolean)：创建是否成功 [cite: 1]。
            * `cardId` (String)：新建卡片的唯一ID [cite: 1]。
            * `message` (String)：结果消息（成功时可返回”创建成功”，失败时返回错误原因）[cite: 1]。
        * **调用方式：** 由前端卡片编辑页在用户填写完内容点击保存时发起 **异步** 调用，将卡片数据通过云函数/后端API传递给服务器 [cite: 1]。服务器在数据库中新建记录后返回结果 [cite: 1]。
        * **错误处理：** 若必填字段缺失或数据格式错误，接口返回`success=false`，`message`说明错误（如”标题不能为空”）[cite: 1]。服务器异常或网络问题也返回`success=false`及相应错误码 [cite: 1]。前端接收到失败响应后应提示用户重试或修改输入 [cite: 1]。
    2.  **EditCard（编辑卡片）** [cite: 1]
        * **接口名称：** `editCard(cardId, data)` [cite: 1]
        * **输入参数：** [cite: 1]
            * `cardId` (String)：待编辑的卡片ID [cite: 1]。
            * `title` (String)：更新后的标题 [cite: 1]。
            * `content` (String)：更新后的内容 [cite: 1]。
            * `imageUrl` (String)：更新后的图片URL（可选）[cite: 1]。
            * `tags` (Array<String>)：更新后的标签ID列表 [cite: 1]。
            * `reminderTime` (Datetime)：更新后的提醒时间（可选）[cite: 1]。
            * `userId` (String)：用户ID（用于权限校验，确保只能编辑自己的卡片）[cite: 1]。
        * **输出参数：** [cite: 1]
            * `success` (Boolean)：编辑操作是否成功 [cite: 1]。
            * `message` (String)：结果信息（例如”保存成功”或失败原因）[cite: 1]。
        * **调用方式：** 前端在卡片编辑页修改内容并点击保存时触发 **异步** 调用，向后端提交修改后的数据 [cite: 1]。后端检索指定`cardId`的记录并更新字段，完成后返回成功标识 [cite: 1]。
        * **错误处理：** 若`cardId`不存在或属于其他用户（权限不足），返回`success=false`和错误消息（如”卡片不存在或无权限”）[cite: 1]。输入校验未通过或数据库更新失败，同样返回失败状态及原因 [cite: 1]。前端据此提示用户相应信息 [cite: 1]。
    3.  **DeleteCard（删除卡片）** [cite: 1]
        * **接口名称：** `deleteCard(cardId, userId)` [cite: 1]
        * **输入参数：** [cite: 1]
            * `cardId` (String)：要删除的卡片ID [cite: 1]。
            * `userId` (String)：用户ID（用于验证操作权限）[cite: 1]。
        * **输出参数：** [cite: 1]
            * `success` (Boolean)：删除是否成功 [cite: 1]。
            * `message` (String)：结果信息（如”删除成功”或失败原因）[cite: 1]。
        * **调用方式：** 用户在卡片列表页选择删除某卡片时（实际UI中应有删除操作，例如长按或在卡片详情页执行删除），前端弹出确认对话框，确认后调用此接口 [cite: 1]。调用为 **异步** ，后端删除数据库中对应记录并返回结果 [cite: 1]。
        * **错误处理：** 若`cardId`不存在或用户无权限删除，返回`success=false`及错误说明（如”无法删除他人卡片”）[cite: 1]。删除操作在后端执行前应确保用户确认，避免误删；若删除过程中发生异常，接口返回失败和错误码，前端提示删除失败并可重试 [cite: 1]。
    4.  **GetCardList（获取卡片列表）** [cite: 1]
        * **接口名称：** `getCardList(userId, [tagId])` [cite: 1]
        * **输入参数：** [cite: 1]
            * `userId` (String)：用户ID，获取该用户所有卡片 [cite: 1]。
            * `tagId` (String)：（可选）标签ID，若提供则筛选具有该标签的卡片 [cite: 1]。
        * **输出参数：** [cite: 1]
            * `success` (Boolean)：获取是否成功 [cite: 1]。
            * `cards` (Array<Object>)：卡片对象列表 [cite: 1]。每个对象包含主要字段如`cardId`, `title`, `content`(或摘要), `tags`, `hasImage`(是否有图片), `reminderTime`等 [cite: 1]。
            * `message` (String)：信息，如错误时的原因说明 [cite: 1]。
        * **调用方式：** 前端卡片列表页初始化或用户下拉刷新时触发 **异步** 调用，请求服务器返回当前用户所有卡片数据 [cite: 1]。可选的`tagId`用于标签筛选功能，当用户在列表页选择按标签过滤时，调用该接口传入特定`tagId`获取对应子集 [cite: 1]。
        * **错误处理：** 如果用户未登录（无有效`userId`）或数据库查询失败，返回`success=false`和错误信息 [cite: 1]。前端收到失败响应时，可提示”无法加载卡片列表”并提供重试操作 [cite: 1]。正常情况下`cards`为数组，即使为空（用户尚未创建卡片）也应返回`success=true`和空列表，以便前端区别空列表和错误 [cite: 1]。

#### 2.2.3 标签模块

* **模块说明：** 提供标签的增删改查接口，支持用户自定义标签以分类管理卡片 [cite: 1]。每个标签仅包含名称（在系统内部有唯一ID），并关联到用户 [cite: 1]。
* **接口列表：** [cite: 1]
    1.  **AddTag（添加标签）** [cite: 1]
        * **接口名称：** `addTag(name, userId)` [cite: 1]
        * **输入参数：** [cite: 1]
            * `name` (String)：新标签名称 [cite: 1]。
            * `userId` (String)：用户ID（将标签归属到该用户名下）[cite: 1]。
        * **输出参数：** [cite: 1]
            * `success` (Boolean)：添加是否成功 [cite: 1]。
            * `tagId` (String)：新建标签的ID（成功时返回）[cite: 1]。
            * `message` (String)：结果信息（如成功或失败原因）[cite: 1]。
        * **调用方式：** 在标签管理页，用户输入名称点击添加按钮时触发 **异步** 调用 [cite: 1]。前端将标签名称发送至后端，后端在数据库中新建一条标签记录，关联用户ID，并返回新标签ID [cite: 1]。
        * **错误处理：** 如果标签名称为空或超长，返回`success=false`且`message`说明”名称不合法” [cite: 1]。若名称已存在（当前用户已创建同名标签），可返回失败以及提示”标签已存在” [cite: 1]。前端应据此提醒用户修改名称 [cite: 1]。添加失败时不改变现有标签数据 [cite: 1]。
    2.  **UpdateTag（修改标签）** [cite: 1]
        * **接口名称：** `updateTag(tagId, newName, userId)` [cite: 1]
        * **输入参数：** [cite: 1]
            * `tagId` (String)：待修改的标签ID [cite: 1]。
            * `newName` (String)：新的标签名称 [cite: 1]。
            * `userId` (String)：用户ID（用于权限校验）[cite: 1]。
        * **输出参数：** [cite: 1]
            * `success` (Boolean)：修改是否成功 [cite: 7]。
            * `message` (String)：结果信息（如成功或失败原因）[cite: 7]。
        * **调用方式：** 用户在标签管理页点击”改”按钮并提交新名称时发起 **异步** 调用 [cite: 1]。前端将标签ID和新名称发送后端，后端校验用户是否为该标签拥有者以及名称合法且不冲突，然后更新数据库中的标签名称 [cite: 1]。
        * **错误处理：** 若`tagId`不存在或不属于该用户，返回`success=false`及错误消息 [cite: 1]。若`newName`非法或与该用户其他标签重名，也返回失败和提示原因 [cite: 1]。成功修改时，后端可能同时更新该用户所有卡片中引用此标签名称的地方（或卡片直接引用标签ID无需更新名称，但前端下次获取卡片列表时会显示新名称）[cite: 1]。
    3.  **DeleteTag（删除标签）** [cite: 1]
        * **接口名称：** `deleteTag(tagId, userId)` [cite: 1]
        * **输入参数：** [cite: 1]
            * `tagId` (String)：要删除的标签ID [cite: 1]。
            * `userId` (String)：用户ID（用于校验标签归属）[cite: 1]。
        * **输出参数：** [cite: 1]
            * `success` (Boolean)：删除是否成功 [cite: 1]。
            * `message` (String)：结果信息（如成功或失败说明）[cite: 1]。
        * **调用方式：** 在标签管理页用户点击”删”按钮并确认后，触发 **异步** 调用此接口 [cite: 1]。前端传递标签ID，后端查验用户权限后，从数据库移除该标签记录 [cite: 1]。
        * **错误处理：** 若标签不存在或不属于该用户，返回失败和提示”找不到标签” [cite: 1]。如果该标签仍被用户的卡片引用，通常仍可删除，但前端应考虑在删除后更新相关卡片的标签列表（可在删除成功后提示用户那些卡片失去该标签）[cite: 1]。任何异常导致删除未完成时，返回`success=false`和错误原因 [cite: 1]。
    4.  **GetTags（获取标签列表）** [cite: 1]
        * **接口名称：** `getTags(userId)` [cite: 1]
        * **输入参数：** [cite: 1]
            * `userId` (String)：用户ID [cite: 1]。
        * **输出参数：** [cite: 1]
            * `success` (Boolean)：获取是否成功 [cite: 1]。
            * `tags` (Array<Object>)：标签列表，每个对象包含`tagId`和`name`等属性 [cite: 1]。
            * `message` (String)：结果信息（错误时包含原因）[cite: 1]。
        * **调用方式：** 前端需要展示标签供选择时（如卡片编辑页点击添加标签时，或标签管理页初始化时），触发 **异步** 调用获取当前用户所有标签 [cite: 1]。后端查询数据库返回标签数组 [cite: 1]。
        * **错误处理：** 如查询失败则返回`success=false`和错误信息 [cite: 1]。通常即使用户没有任何标签也应返回`success=true`和空数组，表示请求成功但列表为空 [cite: 1]。前端收到失败应提示”无法加载标签”，成功但列表为空则提示”暂无标签” [cite: 1]。

#### 2.2.4 提醒模块

* **模块说明：** 负责处理复习提醒功能相关接口，包括设置提醒和取消提醒 [cite: 1]。该模块与微信订阅消息服务集成，使得在指定时间向用户推送卡片复习通知 [cite: 1]。
* **接口列表：** [cite: 1]
    1.  **SetReminder（设置提醒）** [cite: 1]
        * **接口名称：** `setReminder(cardId, time, userId)` [cite: 1]
        * **输入参数：** [cite: 1]
            * `cardId` (String)：要设置提醒的卡片ID [cite: 1]。
            * `time` (Datetime)：提醒触发的日期和时间 [cite: 1]。
            * `userId` (String)：用户ID（用于校验权限）[cite: 1]。
        * **输出参数：** [cite: 1]
            * `success` (Boolean)：设置是否成功 [cite: 1]。
            * `message` (String)：结果信息（如成功或失败原因）[cite: 1]。
        * **调用方式：** 在提醒设置页用户选择好日期时间后点击”设置提醒”触发 **异步** 调用 [cite: 1]。前端提交卡片ID和设定时间至后端，后端将该提醒时间存储（例如保存到卡片记录的提醒字段或单独提醒表中）并调用微信订阅消息API订阅相应通知 [cite: 1]。在约定时间，微信服务器会根据订阅发送提醒消息给用户的小程序 [cite: 1]。
        * **错误处理：** 若`cardId`不存在或不属于当前用户，返回失败和提示 [cite: 1]。若设定时间早于当前时间，返回`success=false`并`message`提示”提醒时间无效” [cite: 1]。订阅消息接口调用失败（例如用户未授权通知或网络错误）也会导致设置失败，此时返回相应错误信息 [cite: 1]。前端应根据不同失败原因提示用户采取相应措施（如授权通知或稍后重试）[cite: 1]。成功设置后，`success=true`且`message`可返回”提醒已设置” [cite: 1]。
    2.  **CancelReminder（取消提醒）** [cite: 1]
        * **接口名称：** `cancelReminder(cardId, userId)` [cite: 1]
        * **输入参数：** [cite: 1]
            * `cardId` (String)：要取消提醒的卡片ID [cite: 1]。
            * `userId` (String)：用户ID（用于校验权限）[cite: 1]。
        * **输出参数：** [cite: 1]
            * `success` (Boolean)：取消是否成功 [cite: 1]。
            * `message` (String)：结果信息（如成功或失败原因）[cite: 1]。
        * **调用方式：** 在提醒设置页用户点击”取消提醒”时触发 **异步** 调用 [cite: 1]。前端发送卡片ID至后端，后端将删除对应卡片的提醒计划（从数据库移除，且无需再触发订阅消息）[cite: 1]。
        * **错误处理：** 如果`cardId`无效或用户无权限，返回失败和说明；如果卡片本就没有设置提醒也可视为失败或直接返回成功无操作 [cite: 1]。一般成功取消会返回`success=true`和”提醒已取消”消息 [cite: 1]。若后端操作失败（例如数据库错误），返回`success=false`和错误码 [cite: 1]。前端收到成功响应后，应更新卡片编辑页的提醒状态为”无”；收到失败则提示用户取消未成功并保留原提醒设置 [cite: 1]。

## 三、数据设计

本章将详细说明记忆卡片小程序的数据库设计，包括逻辑结构、物理实现和数据模型图示 [cite: 1]。

### 3.1 数据库逻辑结构设计

* **主要数据表：** 系统涉及用户、卡片、标签三类核心实体，以及用于建立卡片与标签多对多关系的关联表 [cite: 1]。具体包括：
    * **用户表（User）：** 存储应用用户的基本信息 [cite: 1]。
        * **字段设计：** 用户ID（主键，微信OpenID），昵称 [cite: 1]。用户ID是每个用户的唯一标识，使用微信提供的OpenID作为主键，确保全局唯一；昵称用于记录用户显示名称 [cite: 1]。
    * **卡片表（Card）：** 存储记忆卡片的信息 [cite: 1]。每条记录对应用户创建的一张记忆卡片 [cite: 1]。
        * **字段设计：** 卡片ID（主键，唯一标识卡片，可采用自增ID或UUID）、标题（文本）、内容（文本，大段存储卡片正反面的学习内容）、图片路径（文本URL，可为空）、用户ID（外键，关联用户表）、当前复习轮次（整数，默认0）、总复习次数（整数，默认0）、下次复习时间（日期时间，可为空）、上次复习时间（日期时间，可为空）、最近复习结果（文本，"完全记住"/"部分记住"/"完全忘记"）、是否归档（布尔值，默认false）、归档时间（日期时间，可为空）、创建时间（日期时间）、更新时间（日期时间）[cite: 1]。
        * **约束说明：** 用户ID用于指明该卡片属于哪个用户，必须引用用户表中现有用户；如果”提醒时间”非空，则表示该卡片设置了提醒功能 [cite: 1]。卡片ID为主键，不可重复 [cite: 1]。用户ID是外键，参照用户表的用户ID [cite: 1]。可根据需要对标题或内容设置长度约束 [cite: 1]。
    * **标签表（Tag）：** 存储标签信息 [cite: 1]。每条记录对应用户自定义的一个标签，用于对卡片分类 [cite: 1]。
        * **字段设计：** 标签ID（主键，唯一标识标签）、名称（文本，标签名）、用户ID（外键，关联用户表）[cite: 1]。
        * **约束说明：** 标签名称由用户自定义，同一用户下各标签名最好不重复（可在应用层检查或建立唯一索引约束UNIQUE(userID, 名称)）；用户ID区分标签归属，不同用户可以有相同名称的标签 [cite: 1]。标签ID为主键 [cite: 1]。用户ID为外键，引用用户表用户ID，表示该标签所属的用户 [cite: 1]。
    * **卡片标签关联表（Card_Tag）：** 用于表示卡片与标签的多对多关系 [cite: 1]。如果每张卡片允许打多个标签，而每个标签下可包含多张卡片，则需要此独立的关联表（若不采用卡片记录内数组字段的方式）[cite: 1]。
        * **字段设计：** 卡片ID（外键，关联卡片表）、标签ID（外键，关联标签表）[cite: 1]。联合主键由「卡片ID + 标签ID」组成 [cite: 1]。该表无独立主键字段，每条记录唯一标识”一张卡片-一个标签”的关联关系 [cite: 1]。
        * **约束说明：** 卡片ID必须是有效的卡片表主键，标签ID必须是有效的标签表主键 [cite: 1]。组合主键确保同一张卡片不会重复关联同一个标签 [cite: 1]。通过该表可以高效查询某张卡片拥有哪些标签，或某个标签下有哪些卡片 [cite: 1]。
* **表关系概述：** 用户与卡片是一对多关系，即每位用户可以创建多张卡片；用户与标签也是一对多关系，每个用户可以定义多个标签 [cite: 1]。
    卡片与标签之间是多对多关系，需要通过关联表Card_Tag实现关联 [cite: 1]。在关系模型中，各表通过主键/外键关联保持数据一致性 [cite: 1]。例如，卡片表中的用户ID必须参照用户表中存在的用户；当删除某个用户时（实际应用一般不直接删除用户账号，而是停用），应级联清理其名下的卡片和标签，以保持数据库的一致性 [cite: 1]。同样，当删除卡片或标签时，需要同步删除Card_Tag表中相应的关联记录，避免”悬空”关联 [cite: 1]。通过这样的逻辑结构设计，系统能够表示业务所需的数据关系，并保证数据的完整性和一致性 [cite: 1]。

### 3.2 数据库物理结构设计

* **存储方式：** 记忆卡片小程序采用微信小程序云开发提供的云数据库进行数据存储 [cite: 1, 8]。该数据库属于 **文档型数据库** ，以**集合（collection）**的形式存储数据表，每条记录为一个JSON文档 [cite: 1, 8]。上述逻辑上的”表”将在物理上对应各自的集合，例如User集合、Card集合等 [cite: 1]。其中图片等大文件不直接存储在数据库，而是存储在云存储中，卡片表仅保存图片文件的路径或ID引用 [cite: 1]。
* **主键设计：** 每个集合的记录默认有一个系统生成的唯一 `_id` 字段作为主键 [cite: 1]。在本系统设计中：用户表以微信OpenID用作主键，直接对应 `_id` 或存储在另一个唯一字段 `userID`；卡片表和标签表可以使用云数据库自动生成的 `_id` 作为各自主键（也可以自定义UUID作为 `cardID`/`tagID`）[cite: 1]。关联表Card_Tag可以不使用单独的 `_id`，而是将卡片ID+标签ID的组合设为唯一键标识记录 [cite: 1]。在文档数据库中无法直接定义复合主键约束，但可以通过在应用端确保组合键的唯一性，或在Card_Tag集合上创建基于`cardID`和`tagID`的唯一索引来模拟复合主键约束 [cite: 1]。
* **索引方案：** 为提高查询性能，建议在以下字段上建立索引：[cite: 1]
    * 用户表：`userID`（主键自带索引）[cite: 1]。
    * 卡片表：`userID`（外键，用于按用户查询其所有卡片）；如需按提醒时间筛选卡片，可考虑为`reminderTime`添加索引；若支持全文检索卡片内容，可使用全文搜索索引（云开发支持模糊查询或借助云函数实现）[cite: 1]。
    * 标签表：`userID`（用于按用户列出其标签）；`name`或(`userID`, `name`)组合（用于查找特定标签或避免同用户下重复标签名）[cite: 1]。
    * 关联表：`cardID`和`tagID`（用于查询特定卡片的所有标签，或特定标签的所有卡片）[cite: 1]。可以对`cardID`、`tagID`分别建立单列索引，或建立联合索引加速双向查询 [cite: 1]。
* 由于云数据库是文档存储，没有传统关系型数据库的外键约束机制，因此数据一致性由应用逻辑保证 [cite: 1]。例如，在删除用户时，应用应先删除其关联的卡片和标签记录，再删除用户记录；删除卡片或标签时，也需相应删除关联表中的记录 [cite: 1]。通过良好的索引设计，可保证常用查询（如按用户检索卡片、按标签筛选卡片）的性能，以及维护数据的一致性完整性 [cite: 1]。
* **分区与数据分布：** 鉴于系统数据规模相对较小，当前无需对集合进行分区或分库 [cite: 1]。微信云数据库底层会自动进行数据的分片和扩容，因此应用层不需要手动分区 [cite: 1]。当数据规模增长到一定程度，可考虑按照用户ID进行逻辑分区（例如将不同用户的数据分布到不同分片），但一般由云端自动管理 [cite: 1]。
* **文档字段设计：** 由于采用文档数据库，可利用其灵活性存储嵌套数据结构 [cite: 1]。例如，在卡片表中直接使用数组字段存储关联的标签ID列表也是一种可行的物理设计方式 [cite: 1]。这种嵌入式方案减少了关联表，读取卡片即可得到其标签列表，但在删除标签时需遍历并更新相关卡片文档，维护成本较高 [cite: 1]。本设计选择独立的关联表来体现多对多关系，更贴近关系模型，有助于维护数据规范化和减少冗余 [cite: 1]。同时，这种设计便于扩展（例如将来可在关联表中增加额外属性，如标签赋予卡片的顺序或备注），查询某一标签下的所有卡片时也更直观 [cite: 1]。综上，物理结构采用云数据库的集合存储表数据，结合适当的索引和应用层逻辑，满足小程序对数据读写的性能和一致性要求 [cite: 1]。

### 3.3 数据建模图示

[Image: 数据建模图 - 用户表(PK: 用户ID, 昵称)与卡片表(PK: 卡片ID, FK: 用户ID, 标题, 内容, 图片路径, 提醒时间)为1:N关系。用户表与标签表(PK: 标签ID, FK: 用户ID, 名称)为1:N关系。卡片表与卡片标签关联表(FK: 卡片ID, FK: 标签ID)为1:N关系。标签表与卡片标签关联表也为1:N关系，表示卡片与标签的多对多关系。] [cite: 9]

图 5展示了记忆卡片小程序数据库的实体-关系模型（E-R 图）[cite: 1]。图中包括用户（User）、卡片（Card）、标签（Tag）三个主要实体，以及用于连接卡片和标签的关联实体（Card_Tag），箭头表示实体之间的关系及其基数 [cite: 1]。用户与卡片之间是一对多关系，每个用户可以拥有多张卡片；用户与标签也是一对多关系，每个用户可以创建多个标签 [cite: 1]。卡片与标签之间是多对多关系，通过独立的Card_Tag表实现；一张卡片可以打多个标签，一个标签下也可归类多张卡片 [cite: 1]。在图中，各实体的主键（如用户ID、卡片ID等）用”PK”标注，外键字段（如卡片表的用户ID、标签表的用户ID等）用”FK”标注并通过连线连接到对应的主表，连线上标注的”1:N”表示一对多关系 [cite: 1]。该E-R图直观地表现了数据库中的表结构及其联系，有助于理解数据模型的设计 [cite: 1]。

通过上述图示和说明，清晰地定义了记忆卡片小程序的数据实体、字段结构和相互关系 [cite: 1]。这为后续功能实现提供了可靠的数据库支持，确保系统能够高效地存储、检索和管理用户的卡片及标签数据 [cite: 1]。

## 四、过程设计

### 4.1 用户登录流程

[Image: 用户登录流程图 - 用户打开小程序 -> 步骤1: 检查登录状态是否存在有效会话? (Yes -> 已登录: 跳转主界面, 流程结束; No -> 步骤2: 微信授权登录/用户授权 -> 步骤3: 获取结果/获取OpenID等信息 -> (Success -> 登录成功: 写入用户记录, 跳转主界面; Fail -> 登录失败: 显示错误, 流程终止))] [cite: 10]

图 6用户登录流程：
用户打开记忆卡片小程序后，系统首先检查其是否已有有效的登录状态 [cite: 1]。如果用户已登录过且会话未过期，则直接跳转进入小程序主界面 [cite: 1]。若没有登录态，则系统引导用户进行微信授权登录：小程序调用微信平台获取用户的OpenID等身份信息 [cite: 1]。登录成功后，系统在数据库中创建（或更新）该用户的账户记录，并进入主界面；若微信授权失败或用户拒绝授权，则显示错误提示，流程结束 [cite: 1]。

### 4.2 创建记忆卡片流程

[Image: 创建记忆卡片流程图 - 用户点击新增卡片 -> 进入卡片编辑页 -> 填写卡片信息 (标题, 内容, 图片, 标签) -> 点击保存 -> 系统校验必填项 -> (不完整 -> 提示错误, 要求补充; 完整 -> 写入数据库 -> (保存失败 -> 提示错误; 保存成功 -> 更新前端列表))] [cite: 11]

图 7创建记忆卡片流程：
用户点击”小程序”界面的新增卡片按钮进入卡片编辑页，填写卡片标题、内容以及可选的图片或标签信息 [cite: 1]。提交保存时，系统校验必填项是否填写完整；如果有遗漏则提示错误并要求补充 [cite: 1]。所有必要内容填写完整后，系统将卡片数据写入数据库并同步更新前端列表 [cite: 1]。若保存过程中发生错误（例如网络中断），则向用户提示保存失败 [cite: 1]。

### 4.3 编辑与保存卡片流程

[Image: 编辑记忆卡片流程图 - 用户选择编辑卡片 -> 系统展示卡片详情/进入编辑模式 -> 用户修改卡片信息 -> 点击保存 -> 系统校验输入完整性 -> (不完整 -> 提示完善; 完整 -> 更新数据库 -> (失败 -> 提示错误; 成功 -> 确认保存, 更新前端显示))] [cite: 12]

图 8编辑记忆卡片流程：
用户可以对已有的记忆卡片进行编辑修改 [cite: 1]。首先在卡片列表中选择需编辑的卡片，系统展示卡片详情并进入编辑模式 [cite: 1]。用户修改标题、内容或标签、提醒等信息后点击保存 [cite: 1]。系统校验输入的完整性；若有必填项为空则提示用户完善 [cite: 1]。校验通过后，系统将更新后的卡片数据写回数据库 [cite: 1]。根据数据库返回结果决定后续处理：成功则确认保存并更新前端显示，失败则提示错误需重新尝试 [cite: 1]。

### 4.4 删除卡片流程

[Image: 删除记忆卡片流程图 - 用户选择删除卡片 -> 系统要求确认 -> 用户确认删除 -> 系统从数据库移除卡片 -> (删除失败 -> 提示错误, 保留数据; 删除成功 -> 更新前端列表, 卡片消失)] [cite: 13]

图 9删除记忆卡片流程：
用户选择需要删除的卡片时，系统要求用户进行确认操作以防止误删 [cite: 1]。确认删除后，系统调用后端从数据库中移除该卡片记录，并更新前端列表 [cite: 1]。若删除操作成功，卡片将从用户的卡片列表中消失；若删除失败，则给予用户提示并保留原卡片数据 [cite: 1]。

### 4.5 标签添加与管理流程

[Image: 标签添加与删除流程图 - 用户进入标签管理 -> (添加: 输入标签名 -> 提交 -> 系统校验 (无效/重复 -> 提示错误; 有效 -> 写入数据库, 更新列表)) OR (删除: 选择标签 -> 确认删除 -> 系统从数据库移除 -> (删除失败 -> 提示错误; 删除成功 -> 更新列表))] [cite: 14]

图 10标签添加与删除流程：
用户在标签管理界面可以新增标签或删除已有标签 [cite: 1]。对于添加标签，用户输入标签名称后提交，系统校验名称有效性（不能为空且不重复）；校验通过则将新标签写入数据库并更新标签列表展示，否则提示用户错误原因重新输入 [cite: 1]。对于删除标签，用户选择目标标签并确认删除，系统将其从数据库移除，并从前端列表消失；若删除失败则提示错误 [cite: 1]。

### 4.6 提醒设置与取消流程

[Image: 提醒设置与取消流程图 - 用户为卡片设置/取消提醒 -> (设置: 选择提醒时间 -> 提交 -> (首次需微信通知权限) -> 授权 -> 保存提醒信息到数据库, 登记微信推送 -> (失败 -> 提示错误; 成功 -> 提示确认)) OR (取消: 选择取消提醒 -> 确认 -> 从数据库移除提醒, 取消微信通知 -> (失败 -> 提示错误; 成功 -> 提示已取消))] [cite: 15]

在图 11中用户可以为记忆卡片设置定时复习提醒，并可在无需时取消该提醒 [cite: 1]。对于设置提醒，用户选择提醒时间后提交，若是首次使用提醒功能系统会请求微信通知权限，得到用户授权后将提醒信息保存至数据库并通过微信平台登记定时推送；设置成功则提示确认，失败则提示错误 [cite: 1]。对于取消提醒，用户在卡片编辑界面选择取消已设的提醒并确认，系统从数据库中移除该卡片的提醒设置，并请求微信平台取消相应的通知（如果可行）；成功则提示已取消，失败则提示错误 [cite: 1]。