<view class="archived-cards-container">
  <!-- 顶部标签筛选 -->
  <t-dropdown-menu class="filter-dropdown">
    <t-dropdown-item
      label="标签筛选"
      options="{{tagOptions}}"
      value="{{activeTagId}}"
      bindchange="onTagChange"
    />
  </t-dropdown-menu>

  <!-- 归档卡片列表 -->
  <scroll-view scroll-y class="card-list">
    <t-empty wx:if="{{cards.length === 0}}" description="暂无归档卡片" />

    <block wx:for="{{cards}}" wx:key="id">
      <view class="card-item archived" bind:tap="onCardTap" data-id="{{item.id}}" data-index="{{index}}">
        <view class="card-header">
          <view class="card-icon">
            <view class="icon-placeholder archived-icon"></view>
            <text class="icon-text">已归档</text>
          </view>
          <view class="archived-status">
            <text class="status-text archived-text">{{item.archivedTime}}归档</text>
          </view>
        </view>

        <view class="card-content">
          <view class="card-title">{{item.title}}</view>
          <view class="card-meta">
            <text class="review-info">已完成{{item.totalReviewCount}}次复习</text>
            <text class="create-date">{{item.createTime}}创建</text>
          </view>
        </view>

        <view class="card-tags" wx:if="{{item.tags && item.tags.length > 0}}">
          <t-tag wx:for="{{item.tags}}" wx:for-item="tag" wx:key="id" variant="light" class="tag-item">
            {{tag.name}}
          </t-tag>
        </view>
      </view>
    </block>
  </scroll-view>
</view>
