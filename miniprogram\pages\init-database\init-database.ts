/**
 * 数据库初始化页面
 * 用于创建必要的数据库集合
 */
Page({
  data: {
    loading: false,
    result: null as any
  },

  /**
   * 页面加载时
   */
  onLoad() {
    wx.setNavigationBarTitle({
      title: '数据库初始化'
    });
  },

  /**
   * 初始化数据库
   */
  onInitDatabase() {
    this.setData({
      loading: true,
      result: null
    });

    wx.cloud.callFunction({
      name: 'initDatabase',
      success: (res: any) => {
        console.log('数据库初始化结果', res);
        
        this.setData({
          result: res.result,
          loading: false
        });

        if (res.result && res.result.success) {
          wx.showToast({
            title: '初始化成功！',
            icon: 'success',
            duration: 2000
          });
        } else {
          wx.showModal({
            title: '初始化失败',
            content: res.result?.message || '未知错误',
            showCancel: false
          });
        }
      },
      fail: (err: any) => {
        console.error('调用初始化函数失败', err);
        
        this.setData({
          result: {
            success: false,
            message: '调用初始化函数失败',
            error: err.errMsg || String(err)
          },
          loading: false
        });

        wx.showModal({
          title: '调用失败',
          content: '无法调用初始化函数，请检查网络连接或云函数部署状态',
          showCancel: false
        });
      }
    });
  },

  /**
   * 返回上一页
   */
  onBack() {
    wx.navigateBack();
  }
});
