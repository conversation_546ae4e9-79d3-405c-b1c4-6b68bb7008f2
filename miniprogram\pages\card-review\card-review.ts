/**
 * 卡片复习页面逻辑
 * 显示卡片内容并处理复习结果
 */
Page({
  data: {
    cardId: '',
    cardData: {} as any,
    isTimeToReview: true, // 是否到了复习时间
    reviewOptions: {
      forgot: { round: 0, days: 1 },    // 记得不太行：回退到上一轮
      normal: { round: 0, days: 1 },    // 记忆还行：重复当前轮
      perfect: { round: 0, days: 1 }    // 全部记住：进入下一轮
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options: any) {
    const cardId = options.id;
    if (cardId) {
      this.setData({ cardId });
      this.loadCardData();
    } else {
      wx.showToast({
        title: '卡片ID不存在',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 加载卡片数据
   */
  loadCardData() {
    wx.showLoading({ title: '加载中' });

    wx.cloud.callFunction({
      name: 'getCardDetail',
      data: { cardId: this.data.cardId },
      success: (res: any) => {
        console.log('获取卡片详情成功', res);

        if (res.result && res.result.success) {
          const cardData = res.result.data;

          // 判断是否到了复习时间
          const now = new Date();
          const nextReviewTime = cardData.nextReviewTime ? new Date(cardData.nextReviewTime) : new Date(cardData.createTime);
          const isTimeToReview = now >= nextReviewTime;

          // 计算复习选项
          const reviewOptions = this.calculateReviewOptions(cardData);

          this.setData({
            cardData,
            isTimeToReview,
            reviewOptions
          });
        } else {
          wx.showToast({
            title: '获取卡片失败',
            icon: 'error'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      },
      fail: (err: any) => {
        console.error('获取卡片详情失败', err);
        wx.showToast({
          title: '获取卡片失败',
          icon: 'error'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 计算复习选项
   */
  calculateReviewOptions(cardData: any) {
    const currentRound = cardData.currentRound || 0;

    // 获取用户设置的复习间隔
    const reviewIntervals = this.getUserReviewIntervals();

    // 记得不太行：回退到上一轮（最少第0轮）
    const forgotRound = Math.max(0, currentRound - 1);
    const forgotMinutes = reviewIntervals[forgotRound] || reviewIntervals[0];
    const forgotTimeDisplay = this.convertMinutesToTimeDisplay(forgotMinutes);

    // 记忆还行：重复当前轮
    const normalRound = currentRound;
    const normalMinutes = reviewIntervals[normalRound] || reviewIntervals[reviewIntervals.length - 1];
    const normalTimeDisplay = this.convertMinutesToTimeDisplay(normalMinutes);

    // 全部记住：进入下一轮
    const perfectRound = Math.min(currentRound + 1, reviewIntervals.length - 1);
    const perfectMinutes = reviewIntervals[perfectRound] || reviewIntervals[reviewIntervals.length - 1];
    const perfectTimeDisplay = this.convertMinutesToTimeDisplay(perfectMinutes);

    return {
      forgot: { round: forgotRound, minutes: forgotMinutes, timeDisplay: forgotTimeDisplay },
      normal: { round: normalRound, minutes: normalMinutes, timeDisplay: normalTimeDisplay },
      perfect: { round: perfectRound, minutes: perfectMinutes, timeDisplay: perfectTimeDisplay }
    };
  },

  /**
   * 获取用户设置的复习间隔
   */
  getUserReviewIntervals() {
    try {
      const settings = wx.getStorageSync('ebbinghausSettings');
      if (settings) {
        const reviewIntervals = {
          review1: 5,
          review2: 30,
          review3: 12 * 60,
          review4: 24 * 60,
          review5: 2 * 24 * 60,
          review6: 4 * 24 * 60,
          review7: 7 * 24 * 60,
          review8: 15 * 24 * 60,
          review9: 30 * 24 * 60,
          review10: 60 * 24 * 60,
          review11: 90 * 24 * 60,
          review12: 120 * 24 * 60
        };

        const enabledIntervals = [];
        for (const key in settings) {
          if (settings[key] && reviewIntervals[key]) {
            enabledIntervals.push(reviewIntervals[key]);
          }
        }

        return enabledIntervals.length > 0 ? enabledIntervals : [5, 30, 12 * 60, 24 * 60, 2 * 24 * 60, 7 * 24 * 60, 30 * 24 * 60];
      }
    } catch (error) {
      console.error('获取复习设置失败', error);
    }

    // 默认间隔（分钟）
    return [5, 30, 12 * 60, 24 * 60, 2 * 24 * 60, 7 * 24 * 60, 30 * 24 * 60];
  },

  /**
   * 将分钟转换为显示文本
   */
  convertMinutesToTimeDisplay(minutes: number): string {
    if (minutes < 60) {
      return `${minutes}分钟`;
    } else if (minutes < 24 * 60) {
      const hours = Math.round(minutes / 60);
      return `${hours}小时`;
    } else {
      const days = Math.round(minutes / (24 * 60));
      return `${days}天`;
    }
  },

  /**
   * 处理复习结果
   */
  onReviewResult(e: any) {
    const result = e.currentTarget.dataset.result;

    wx.showModal({
      title: '确认复习结果',
      content: '确定要提交这个复习结果吗？',
      success: (res) => {
        if (res.confirm) {
          this.submitReviewResult(result);
        }
      }
    });
  },

  /**
   * 提交复习结果
   */
  submitReviewResult(result: string) {
    wx.showLoading({ title: '提交中' });

    const reviewOption = this.data.reviewOptions[result as keyof typeof this.data.reviewOptions];

    // 获取用户设置的复习间隔
    const reviewIntervals = this.getUserReviewIntervals();

    // 计算下次复习时间（使用分钟）
    const nextReviewTime = new Date();
    const intervalMinutes = reviewOption.minutes;
    nextReviewTime.setTime(nextReviewTime.getTime() + intervalMinutes * 60 * 1000);

    // 检查是否完成了所有复习轮次（选择"全部记住"且已到最后一轮）
    const isCompleted = result === 'perfect' && reviewOption.round >= reviewIntervals.length - 1;

    // 提前复习和正常复习都会影响复习计划，更新轮次和次数
    const updateData: any = {
      cardId: this.data.cardId,
      reviewResult: result,
      nextReviewTime: nextReviewTime.getTime(),
      isEarlyReview: !this.data.isTimeToReview,
      autoArchive: isCompleted, // 标记是否需要自动归档
      currentRound: reviewOption.round,
      totalReviewCount: (this.data.cardData.totalReviewCount || 0) + 1
    };

    wx.cloud.callFunction({
      name: 'updateReviewResult',
      data: updateData,
      success: (res: any) => {
        console.log('提交复习结果成功', res);

        if (res.result && res.result.success) {
          if (isCompleted) {
            wx.showToast({
              title: '恭喜！卡片已完成所有复习并自动归档',
              icon: 'success',
              duration: 3000
            });

            setTimeout(() => {
              wx.navigateBack();
            }, 3000);
          } else {
            // 复习完成但未归档，请求下次提醒授权
            this.requestNextReminderAuth(result);
          }
        } else {
          wx.showToast({
            title: '提交失败，请重试',
            icon: 'error'
          });
        }
      },
      fail: (err: any) => {
        console.error('提交复习结果失败', err);
        wx.showToast({
          title: '提交失败，请重试',
          icon: 'error'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 请求下次提醒授权
   */
  requestNextReminderAuth(reviewResult: string) {
    const templateId = 'lNNqgUJU3ITeDC9hlQosOO-a_bF4iF8EM_TpTF3Ap4c';
    const reviewOption = this.data.reviewOptions[reviewResult as keyof typeof this.data.reviewOptions];

    wx.showModal({
      title: '开启下次提醒',
      content: `复习完成！是否开启下次复习提醒？\n下次复习时间：${reviewOption.timeDisplay}后`,
      confirmText: '开启提醒',
      cancelText: '暂不开启',
      success: (modalRes) => {
        if (modalRes.confirm) {
          // 请求订阅消息授权
          wx.requestSubscribeMessage({
            tmplIds: [templateId],
            success: (subscribeRes: any) => {
              console.log('下次提醒授权结果', subscribeRes);

              if (subscribeRes[templateId] === 'accept') {
                wx.showToast({
                  title: '复习完成，已开启下次提醒！',
                  icon: 'success',
                  duration: 2000
                });
              } else {
                wx.showToast({
                  title: '复习完成',
                  icon: 'success',
                  duration: 2000
                });
              }

              setTimeout(() => {
                wx.navigateBack();
              }, 2000);
            },
            fail: (err: any) => {
              console.error('下次提醒授权失败', err);
              wx.showToast({
                title: '复习完成',
                icon: 'success',
                duration: 2000
              });

              setTimeout(() => {
                wx.navigateBack();
              }, 2000);
            }
          });
        } else {
          // 用户选择暂不开启
          wx.showToast({
            title: '复习完成',
            icon: 'success',
            duration: 2000
          });

          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
        }
      }
    });
  }
});
