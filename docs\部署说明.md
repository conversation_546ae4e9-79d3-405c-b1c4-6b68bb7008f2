# 记忆卡片小程序部署说明

## 环境要求

### 开发工具
- 微信开发者工具（最新版本）
- Node.js 14.0 或以上版本

### 账号准备
- 微信小程序账号（已认证）
- 微信云开发环境

## 部署步骤

### 1. 获取项目代码
```bash
# 克隆项目（如果是从代码仓库获取）
git clone [项目地址]
cd 记忆卡片小程序
```

### 2. 安装依赖
```bash
# 安装项目依赖
npm install
```

### 3. 配置云开发环境

#### 3.1 创建云开发环境
1. 在微信开发者工具中打开项目
2. 点击"云开发"按钮
3. 创建新的云开发环境
4. 记录环境ID（格式如：cloud1-xxxxxxx）

#### 3.2 修改环境配置
在 `miniprogram/app.ts` 文件中修改云环境ID：
```typescript
wx.cloud.init({
  env: 'your-cloud-env-id', // 替换为你的云环境ID
  traceUser: true
})
```

### 4. 部署云函数

#### 4.1 上传云函数
在微信开发者工具中，右键点击每个云函数文件夹，选择"上传并部署：云端安装依赖"：

- `cloudfunctions/login` - 用户登录
- `cloudfunctions/updateUserInfo` - 更新用户信息
- `cloudfunctions/saveCard` - 保存卡片
- `cloudfunctions/getCards` - 获取卡片列表
- `cloudfunctions/getCardDetail` - 获取卡片详情
- `cloudfunctions/deleteCard` - 删除卡片
- `cloudfunctions/updateReviewResult` - 更新复习结果
- `cloudfunctions/restoreCard` - 恢复归档卡片
- `cloudfunctions/archiveCard` - 归档卡片
- `cloudfunctions/saveTag` - 保存标签
- `cloudfunctions/getTags` - 获取标签列表
- `cloudfunctions/deleteTag` - 删除标签
- `cloudfunctions/scheduleCardReminder` - 精确时间复习提醒调度
- `cloudfunctions/sendDelayedReminder` - 延迟发送提醒
- `cloudfunctions/checkSubscribeStatus` - 检查订阅状态
- `cloudfunctions/updateSubscribeStatus` - 更新订阅状态
- `cloudfunctions/requestSubscribeMessage` - 请求订阅消息授权

#### 4.2 配置云函数权限
确保所有云函数都有正确的数据库访问权限。

**重要**：配置订阅消息权限
1. 在云开发控制台进入"设置" → "权限设置"
2. 找到"订阅消息"权限并开启
3. 确保云函数有调用微信开放接口的权限

### 5. 初始化数据库

#### 5.1 创建数据库集合
在云开发控制台的数据库中创建以下集合：

1. **users** - 用户信息
   - 权限：仅创建者可读写

2. **cards** - 记忆卡片
   - 权限：仅创建者可读写

3. **tags** - 标签
   - 权限：仅创建者可读写

4. **reminders** - 提醒记录
   - 权限：仅创建者可读写

#### 5.2 配置数据库索引（可选）
为提高查询性能，可以为以下字段创建索引：
- `cards` 集合：`_openid`, `createTime`, `tagIds`
- `tags` 集合：`_openid`, `name`
- `reminders` 集合：`_openid`, `cardId`, `reminderTime`

### 6. 配置云存储

#### 6.1 设置存储权限
在云开发控制台的存储中，确保有以下文件夹的访问权限：
- `images/` - 卡片图片
- `avatars/` - 用户头像

### 7. 配置订阅消息

#### 7.1 订阅消息模板配置
已配置的订阅消息模板ID：`lNNqgUJU3ITeDC9hlQosOO-a_bF4iF8EM_TpTF3Ap4c`

#### 7.2 订阅消息功能
- 首次登录时会自动提示用户开启消息推送
- 在个人资料页面可以管理订阅消息设置
- 系统会在卡片复习时间到达时自动发送精确提醒

#### 7.3 配置定时任务（可选）
新的提醒机制使用精确时间触发，不再需要定时轮询。但如果需要处理长期提醒（超过24小时），可以配置：

1. 在云开发控制台的"云函数"中找到 `scheduleCardReminder` 函数
2. 该函数已配置为按需调用，无需额外定时器配置
3. 系统会自动在卡片创建和复习时安排精确的提醒时间



### 8. 测试部署

#### 8.1 本地测试
1. 在微信开发者工具中预览
2. 测试主要功能：
   - 登录功能
   - 卡片增删改查
   - 标签管理
   - 提醒设置
   - 图片上传

#### 8.2 真机测试
1. 使用微信开发者工具的预览功能
2. 在真机上测试所有功能

### 9. 发布上线

#### 9.1 提交审核
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 在微信公众平台提交审核

#### 9.2 发布版本
审核通过后，在微信公众平台发布版本。

## 配置文件说明

### project.config.json
小程序项目配置文件，包含：
- 项目基本信息
- 编译配置
- 云开发配置

### app.json
小程序全局配置文件，包含：
- 页面路径
- 窗口配置
- 标签栏配置
- 组件配置

### tsconfig.json
TypeScript 配置文件，定义编译选项。

## 常见问题

### Q: 云函数部署失败
A: 检查网络连接，确保云开发环境已正确创建，重试部署。

### Q: 数据库权限错误
A: 确保数据库集合权限设置为"仅创建者可读写"。

### Q: 图片上传失败
A: 检查云存储权限配置，确保有上传权限。

### Q: 登录失败
A: 检查云环境ID是否正确配置，确保login云函数已正确部署。

### Q: 订阅消息不工作
A: 检查是否已申请订阅消息模板，模板ID是否正确配置。

## 性能优化建议

### 1. 数据库优化
- 为常用查询字段创建索引
- 合理设计数据结构
- 避免深度嵌套查询

### 2. 云函数优化
- 减少不必要的数据库查询
- 使用连接池
- 合理设置超时时间

### 3. 前端优化
- 图片懒加载
- 合理使用缓存
- 减少不必要的页面刷新

## 监控和维护

### 1. 云开发控制台
定期检查：
- 云函数调用量和错误率
- 数据库读写量
- 存储使用量

### 2. 小程序数据助手
监控：
- 用户访问量
- 页面性能
- 错误日志

### 3. 定期备份
- 定期导出重要数据
- 备份云函数代码
- 记录配置变更

## 版本更新

### 更新流程
1. 修改代码
2. 测试功能
3. 更新版本号
4. 部署云函数（如有变更）
5. 上传小程序代码
6. 提交审核
7. 发布新版本

### 版本管理
- 使用语义化版本号
- 记录更新日志
- 保留历史版本备份
