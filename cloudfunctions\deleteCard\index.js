// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 删除卡片云函数
 * @param {Object} event - 云函数调用参数
 * @param {string} event.cardId - 要删除的卡片ID
 * @param {Object} context - 云函数上下文
 * @returns {Object} 删除结果
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    const { cardId } = event

    if (!cardId) {
      return {
        success: false,
        message: '缺少卡片ID参数'
      }
    }

    // 先查询卡片是否存在且属于当前用户
    const card = await db.collection('cards').doc(cardId).get()

    if (!card.data || card.data._openid !== openid) {
      return {
        success: false,
        message: '卡片不存在或无权限删除'
      }
    }

    // 删除卡片相关的提醒记录
    try {
      await db.collection('reminders').where({
        cardId: cardId,
        _openid: openid
      }).remove()
    } catch (reminderError) {
      console.log('删除提醒记录失败，但不影响卡片删除', reminderError)
    }

    // 删除卡片
    await db.collection('cards').doc(cardId).remove()

    return {
      success: true,
      message: '卡片删除成功'
    }
  } catch (error) {
    console.error('删除卡片失败', error)
    return {
      success: false,
      error: error
    }
  }
}
