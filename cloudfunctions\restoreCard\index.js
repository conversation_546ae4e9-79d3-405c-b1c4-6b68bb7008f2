// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 恢复卡片云函数
 * @param {Object} event - 云函数调用参数
 * @param {string} event.cardId - 卡片ID
 * @param {Object} context - 云函数上下文
 * @returns {Object} 恢复结果
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { cardId } = event

  try {
    // 验证参数
    if (!cardId) {
      return {
        success: false,
        message: '卡片ID不能为空'
      }
    }

    // 检查卡片是否存在且属于当前用户
    const cardResult = await db.collection('cards').doc(cardId).get()
    
    if (!cardResult.data) {
      return {
        success: false,
        message: '卡片不存在'
      }
    }

    if (cardResult.data._openid !== wxContext.OPENID) {
      return {
        success: false,
        message: '无权限操作此卡片'
      }
    }

    // 恢复卡片，重置复习计划
    const nextReviewTime = new Date()
    nextReviewTime.setDate(nextReviewTime.getDate() + 1) // 恢复后1天开始复习

    const updateResult = await db.collection('cards').doc(cardId).update({
      data: {
        archived: false,
        archivedTime: null,
        currentRound: 0, // 重置复习轮次
        nextReviewTime: nextReviewTime, // 重新设置下次复习时间
        updateTime: db.serverDate()
      }
    })

    console.log('恢复卡片成功', updateResult)

    return {
      success: true,
      message: '卡片恢复成功',
      data: {
        updated: updateResult.stats.updated
      }
    }
  } catch (error) {
    console.error('恢复卡片失败', error)
    return {
      success: false,
      message: '恢复失败',
      error: error
    }
  }
}
