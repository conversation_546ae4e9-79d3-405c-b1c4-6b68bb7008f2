<view class="review-container">
  <!-- 卡片内容 -->
  <view class="card-content">
    <view class="card-header">
      <text class="card-title">{{cardData.title}}</text>
      <text class="review-count">第{{(cardData.totalReviewCount || 0) + 1}}次复习</text>
    </view>

    <view class="card-body">
      <text class="card-text">{{cardData.content}}</text>

      <view wx:if="{{cardData.imageUrl}}" class="card-image">
        <image src="{{cardData.imageUrl}}" mode="aspectFit" class="image" />
      </view>

      <view wx:if="{{cardData.tags && cardData.tags.length > 0}}" class="card-tags">
        <t-tag wx:for="{{cardData.tags}}" wx:for-item="tag" wx:key="id" variant="light" class="tag-item">
          {{tag.name}}
        </t-tag>
      </view>
    </view>
  </view>

  <!-- 复习效果选择 -->
  <view class="review-actions">
    <text class="actions-title">本次复习效果如何？</text>

    <view class="action-buttons">
      <t-button
        theme="danger"
        size="large"
        block
        class="action-btn"
        bind:tap="onReviewResult"
        data-result="forgot"
      >
        记得不太行 (回退到第{{reviewOptions.forgot.round + 1}}轮，{{reviewOptions.forgot.timeDisplay}}后复习)
      </t-button>

      <t-button
        theme="warning"
        size="large"
        block
        class="action-btn"
        bind:tap="onReviewResult"
        data-result="normal"
      >
        记忆还行 (重复第{{reviewOptions.normal.round + 1}}轮，{{reviewOptions.normal.timeDisplay}}后复习)
      </t-button>

      <t-button
        theme="success"
        size="large"
        block
        class="action-btn"
        bind:tap="onReviewResult"
        data-result="perfect"
      >
        全部记住 (进入第{{reviewOptions.perfect.round + 1}}轮，{{reviewOptions.perfect.timeDisplay}}后复习)
      </t-button>
    </view>

    <view class="early-review-tip" wx:if="{{!isTimeToReview}}">
      <text>⚠️ 提前复习会影响原来的复习计划</text>
    </view>
  </view>
</view>
