.review-container {
  padding: 40rpx;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.card-content {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.card-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2d3436;
}

.review-count {
  font-size: 24rpx;
  color: #636e72;
  background-color: #f8f9fa;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.card-body {
  line-height: 1.6;
}

.card-text {
  font-size: 32rpx;
  color: #2d3436;
  display: block;
  margin-bottom: 24rpx;
}

.card-image {
  margin: 24rpx 0;
  text-align: center;
}

.image {
  max-width: 100%;
  max-height: 400rpx;
  border-radius: 12rpx;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 24rpx;
}

.tag-item {
  font-size: 24rpx;
}

.review-actions {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.actions-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2d3436;
  display: block;
  text-align: center;
  margin-bottom: 32rpx;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.action-btn {
  font-size: 28rpx !important;
  padding: 24rpx !important;
  border-radius: 12rpx !important;
  margin-bottom: 16rpx !important;
}

.early-review-tip {
  margin-top: 24rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  text-align: center;
}

.early-review-tip text {
  font-size: 26rpx;
  color: #636e72;
}
