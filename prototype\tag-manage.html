<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签管理页</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- TDesign风格的自定义配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0052d9', // TDesign主色调
                        success: '#00a870',
                        warning: '#ed7b2f',
                        error: '#e34d59',
                        'gray-1': '#f3f3f3',
                        'gray-2': '#eee',
                        'gray-3': '#e7e7e7',
                        'gray-4': '#dcdcdc',
                        'gray-5': '#c5c5c5',
                        'gray-6': '#a6a6a6',
                        'gray-7': '#8b8b8b',
                        'gray-8': '#5e5e5e',
                        'gray-9': '#3f3f3f',
                        'gray-10': '#2c2c2c',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #fff;
        }
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 56px;
            background-color: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
            z-index: 100;
        }
        .tag-container {
            margin-top: 66px;
            padding: 10px 16px;
            padding-bottom: 20px;
        }
        .tag-add-form {
            display: flex;
            margin-bottom: 20px;
        }
        .tag-input {
            flex: 1;
            padding: 12px;
            border: 1px solid #dcdcdc;
            border-radius: 6px 0 0 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        .tag-input:focus {
            border-color: #0052d9;
            outline: none;
        }
        .tag-add-btn {
            padding: 0 20px;
            background-color: #00a870;
            color: white;
            border: none;
            border-radius: 0 6px 6px 0;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .tag-add-btn:hover {
            background-color: #009a65;
        }
        .tag-list {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .tag-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            background-color: #fff;
            border-bottom: 1px solid #f0f0f0;
        }
        .tag-item:last-child {
            border-bottom: none;
        }
        .tag-name {
            font-size: 16px;
            color: #333;
        }
        .tag-actions {
            display: flex;
            gap: 12px;
        }
        .tag-action-btn {
            width: 32px;
            height: 32px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .tag-edit-btn {
            color: #0052d9;
            background-color: #e7f1ff;
        }
        .tag-edit-btn:hover {
            background-color: #d4e5ff;
        }
        .tag-delete-btn {
            color: #e34d59;
            background-color: #ffecee;
        }
        .tag-delete-btn:hover {
            background-color: #ffe0e3;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header">
        <button class="flex items-center text-gray-8">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m15 18-6-6 6-6"/>
            </svg>
            <span class="ml-1">返回</span>
        </button>
        <div class="text-lg font-medium">标签管理</div>
        <div class="w-8 h-8"></div> <!-- 占位元素，保持标题居中 -->
    </div>
    
    <!-- 标签管理容器 -->
    <div class="tag-container">
        <!-- 添加标签表单 -->
        <div class="tag-add-form">
            <input type="text" class="tag-input" placeholder="请输入标签名称">
            <button class="tag-add-btn">添加</button>
        </div>
        
        <!-- 标签列表 -->
        <div class="tag-list">
            <!-- 标签项 1 -->
            <div class="tag-item">
                <div class="tag-name">英语词汇</div>
                <div class="tag-actions">
                    <div class="tag-action-btn tag-edit-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M17 3a2.85 2.85 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                        </svg>
                    </div>
                    <div class="tag-action-btn tag-delete-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M3 6h18"></path>
                            <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                            <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                        </svg>
                    </div>
                </div>
            </div>
            
            <!-- 标签项 2 -->
            <div class="tag-item">
                <div class="tag-name">公式定理</div>
                <div class="tag-actions">
                    <div class="tag-action-btn tag-edit-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M17 3a2.85 2.85 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                        </svg>
                    </div>
                    <div class="tag-action-btn tag-delete-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M3 6h18"></path>
                            <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                            <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                        </svg>
                    </div>
                </div>
            </div>
            
            <!-- 标签项 3 -->
            <div class="tag-item">
                <div class="tag-name">每日复盘</div>
                <div class="tag-actions">
                    <div class="tag-action-btn tag-edit-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M17 3a2.85 2.85 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                        </svg>
                    </div>
                    <div class="tag-action-btn tag-delete-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M3 6h18"></path>
                            <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                            <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                        </svg>
                    </div>
                </div>
            </div>
            
            <!-- 标签项 4 -->
            <div class="tag-item">
                <div class="tag-name">学习方法</div>
                <div class="tag-actions">
                    <div class="tag-action-btn tag-edit-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M17 3a2.85 2.85 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                        </svg>
                    </div>
                    <div class="tag-action-btn tag-delete-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M3 6h18"></path>
                            <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                            <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                        </svg>
                    </div>
                </div>
            </div>
            
            <!-- 标签项 5 -->
            <div class="tag-item">
                <div class="tag-name">工作笔记</div>
                <div class="tag-actions">
                    <div class="tag-action-btn tag-edit-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M17 3a2.85 2.85 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                        </svg>
                    </div>
                    <div class="tag-action-btn tag-delete-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M3 6h18"></path>
                            <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                            <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 添加标签功能
        const tagInput = document.querySelector('.tag-input');
        const tagAddBtn = document.querySelector('.tag-add-btn');
        const tagList = document.querySelector('.tag-list');
        
        tagAddBtn.addEventListener('click', function() {
            const tagName = tagInput.value.trim();
            if (tagName) {
                // 创建新标签元素
                const newTag = document.createElement('div');
                newTag.className = 'tag-item';
                newTag.innerHTML = `
                    <div class="tag-name">${tagName}</div>
                    <div class="tag-actions">
                        <div class="tag-action-btn tag-edit-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M17 3a2.85 2.85 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                            </svg>
                        </div>
                        <div class="tag-action-btn tag-delete-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M3 6h18"></path>
                                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                            </svg>
                        </div>
                    </div>
                `;
                
                // 添加到列表开头
                tagList.insertBefore(newTag, tagList.firstChild);
                
                // 清空输入框
                tagInput.value = '';
                
                // 添加事件监听器到新标签的按钮
                addTagEventListeners(newTag);
            }
        });
        
        // 为标签的编辑和删除按钮添加事件监听器
        function addTagEventListeners(tagItem) {
            const editBtn = tagItem.querySelector('.tag-edit-btn');
            const deleteBtn = tagItem.querySelector('.tag-delete-btn');
            
            editBtn.addEventListener('click', function() {
                const tagNameElement = tagItem.querySelector('.tag-name');
                const currentName = tagNameElement.textContent;
                
                // 创建一个简单的编辑界面
                const input = document.createElement('input');
                input.type = 'text';
                input.value = currentName;
                input.className = 'tag-input';
                input.style.width = '100%';
                
                // 替换标签名称为输入框
                tagNameElement.innerHTML = '';
                tagNameElement.appendChild(input);
                input.focus();
                
                // 失去焦点时保存
                input.addEventListener('blur', function() {
                    tagNameElement.textContent = input.value.trim() || currentName;
                });
                
                // 按回车键保存
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        tagNameElement.textContent = input.value.trim() || currentName;
                    }
                });
            });
            
            deleteBtn.addEventListener('click', function() {
                if (confirm('确定要删除这个标签吗？')) {
                    tagItem.remove();
                }
            });
        }
        
        // 为现有标签添加事件监听器
        document.querySelectorAll('.tag-item').forEach(addTagEventListeners);
    </script>
</body>
</html>
