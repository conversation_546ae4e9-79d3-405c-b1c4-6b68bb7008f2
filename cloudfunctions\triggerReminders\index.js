// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
  traceUser: true
})

const db = cloud.database()

/**
 * 手动触发提醒处理云函数
 * 简单直接：立即处理所有到期提醒
 * @param {Object} event - 云函数调用参数
 * @param {Object} context - 云函数上下文
 * @returns {Object} 处理结果
 */
exports.main = async (event, context) => {
  try {
    console.log('手动触发提醒处理...')

    // 先检查提醒记录
    const now = new Date()
    const remindersResult = await db.collection('reminders').where({
      status: 'scheduled'
    }).get()

    console.log(`当前数据库中有 ${remindersResult.data.length} 条待处理的提醒记录`)

    // 显示提醒记录详情
    remindersResult.data.forEach((reminder, index) => {
      const reminderTime = new Date(reminder.reminderTime)
      const delay = reminderTime.getTime() - now.getTime()
      const delayMinutes = Math.round(delay / 60000)

      console.log(`提醒 ${index + 1}: 卡片 ${reminder.cardId}, 预定时间 ${reminderTime.toISOString()}, 距离现在 ${delayMinutes} 分钟`)
    })

    // 调用 processReminders 云函数
    const result = await cloud.callFunction({
      name: 'processReminders'
    })

    console.log('提醒处理完成:', JSON.stringify(result.result, null, 2))

    return {
      success: true,
      message: '手动触发提醒处理完成',
      data: {
        totalScheduledReminders: remindersResult.data.length,
        reminders: remindersResult.data.map(r => ({
          cardId: r.cardId,
          reminderTime: r.reminderTime,
          status: r.status,
          delayHours: r.delayHours
        })),
        processResult: result.result
      }
    }

  } catch (error) {
    console.error('云函数执行失败', error)
    return {
      success: false,
      message: '云函数执行失败',
      error: error.message || String(error)
    }
  }
}
