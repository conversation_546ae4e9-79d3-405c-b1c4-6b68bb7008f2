<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>记忆卡片小程序原型</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 添加TDesign风格的自定义配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0052d9', // TDesign主色调
                        success: '#00a870',
                        warning: '#ed7b2f',
                        error: '#e34d59',
                        'gray-1': '#f3f3f3',
                        'gray-2': '#eee',
                        'gray-3': '#e7e7e7',
                        'gray-4': '#dcdcdc',
                        'gray-5': '#c5c5c5',
                        'gray-6': '#a6a6a6',
                        'gray-7': '#8b8b8b',
                        'gray-8': '#5e5e5e',
                        'gray-9': '#3f3f3f',
                        'gray-10': '#2c2c2c',
                    },
                    borderRadius: {
                        'tdesign': '3px',
                    }
                }
            }
        }
    </script>
    <style>
        /* 模拟iPhone 15 Pro的屏幕规格 */
        .iphone-frame {
            width: 393px; /* iPhone 15 Pro 宽度 */
            height: 852px; /* iPhone 15 Pro 高度 */
            border-radius: 40px; /* 圆角设计 */
            overflow: hidden;
            border: 10px solid #1c1c1e;
            position: relative;
            background-color: #fff;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            margin: 20px;
        }
        /* 顶部刘海区域 */
        .notch {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 30px;
            background-color: #1c1c1e;
            border-bottom-left-radius: 15px;
            border-bottom-right-radius: 15px;
            z-index: 10;
        }
        /* 页面容器样式 */
        .prototype-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            padding: 20px;
            background-color: #f5f5f5;
        }
        /* 页面标题样式 */
        .page-title {
            width: 100%;
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        /* 页面描述样式 */
        .page-description {
            width: 100%;
            text-align: center;
            margin-bottom: 30px;
            font-size: 16px;
            color: #666;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="prototype-container">
        <h1 class="page-title">记忆卡片小程序原型</h1>
        <p class="page-description">以下是记忆卡片小程序的各个界面原型，模拟iPhone 15 Pro的屏幕规格</p>
        
        <!-- 登录页面 -->
        <div class="iphone-frame">
            <div class="notch"></div>
            <iframe src="login.html" frameborder="0" width="100%" height="100%" title="登录页面"></iframe>
            <p class="text-center mt-2 font-medium">登录页面</p>
        </div>
        
        <!-- 卡片列表页 -->
        <div class="iphone-frame">
            <div class="notch"></div>
            <iframe src="card-list.html" frameborder="0" width="100%" height="100%" title="卡片列表页"></iframe>
            <p class="text-center mt-2 font-medium">卡片列表页</p>
        </div>
        
        <!-- 卡片编辑页 -->
        <div class="iphone-frame">
            <div class="notch"></div>
            <iframe src="card-edit.html" frameborder="0" width="100%" height="100%" title="卡片编辑页"></iframe>
            <p class="text-center mt-2 font-medium">卡片编辑页</p>
        </div>
        
        <!-- 标签管理页 -->
        <div class="iphone-frame">
            <div class="notch"></div>
            <iframe src="tag-manage.html" frameborder="0" width="100%" height="100%" title="标签管理页"></iframe>
            <p class="text-center mt-2 font-medium">标签管理页</p>
        </div>
        
        <!-- 提醒设置页 -->
        <div class="iphone-frame">
            <div class="notch"></div>
            <iframe src="reminder-setting.html" frameborder="0" width="100%" height="100%" title="提醒设置页"></iframe>
            <p class="text-center mt-2 font-medium">提醒设置页</p>
        </div>
    </div>
</body>
</html>