<view class="card-list-container">
  <!-- 顶部标签筛选 -->
  <t-dropdown-menu class="filter-dropdown">
    <t-dropdown-item
      label="标签筛选"
      options="{{tagOptions}}"
      value="{{activeTagId}}"
      bindchange="onTagChange"
    />
  </t-dropdown-menu>

  <!-- 卡片列表 -->
  <scroll-view scroll-y class="card-list">
    <t-empty wx:if="{{cards.length === 0}}" description="暂无卡片，点击右下角添加" />

    <block wx:for="{{cards}}" wx:key="id">
      <view class="card-item {{item.needReview ? 'need-review' : 'normal'}}" bind:tap="onCardTap" data-id="{{item.id}}" data-index="{{index}}">
        <view class="card-header">
          <view class="card-icon">
            <view class="icon-placeholder"></view>
            <text class="icon-text">内容卡片</text>
          </view>
          <view class="review-status">
            <text class="status-text {{item.needReview ? 'need-review-text' : 'normal-text'}}">{{item.timeUntilReview}}</text>
          </view>
        </view>

        <view class="card-content">
          <view class="card-title">{{item.title}}</view>
          <view class="card-meta">
            <text class="review-info">第{{item.currentRound + 1}}轮复习 · 已复习{{item.totalReviewCount}}次</text>
            <text class="create-date">{{item.createTime}}创建</text>
          </view>
        </view>

        <view class="card-tags" wx:if="{{item.tags && item.tags.length > 0}}">
          <t-tag wx:for="{{item.tags}}" wx:for-item="tag" wx:key="id" variant="light" class="tag-item">
            {{tag.name}}
          </t-tag>
        </view>
      </view>
    </block>
  </scroll-view>

  <!-- 添加按钮 -->
  <view class="add-button-wrapper">
    <t-fab
      icon="add"
      theme="primary"
      size="large"
      bind:click="onAddCard"
    />
  </view>

  <!-- 自定义TabBar -->
  <custom-tabbar value="card-list" />
</view>
