// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
  traceUser: true
})

const db = cloud.database()

/**
 * 发送延迟的复习提醒
 * @param {Object} event - 云函数调用参数
 * @param {string} event.openid - 用户openid
 * @param {string} event.cardId - 卡片ID
 * @param {Object} context - 云函数上下文
 * @returns {Object} 发送结果
 */
exports.main = async (event, context) => {
  const { openid, cardId } = event

  try {
    console.log(`发送延迟提醒: 用户 ${openid}, 卡片 ${cardId}`)

    // 获取最新的卡片信息
    const cardResult = await db.collection('cards').doc(cardId).get()
    if (!cardResult.data) {
      console.log('卡片已被删除，取消发送提醒')

      // 清理提醒记录
      await db.collection('reminders').where({
        cardId: cardId,
        status: 'scheduled'
      }).remove()

      return {
        success: true,
        message: '卡片已删除，提醒已取消'
      }
    }

    const card = cardResult.data

    // 检查卡片是否还需要复习
    if (card.isArchived) {
      console.log('卡片已归档，取消发送提醒')

      // 清理提醒记录
      await db.collection('reminders').where({
        cardId: cardId,
        status: 'scheduled'
      }).remove()

      return {
        success: true,
        message: '卡片已归档，提醒已取消'
      }
    }

    // 检查复习时间是否已经变更
    const now = new Date()
    const nextReviewTime = new Date(card.nextReviewTime)

    if (nextReviewTime > now) {
      console.log('复习时间已变更，重新安排提醒')

      // 重新安排提醒
      await cloud.callFunction({
        name: 'scheduleCardReminder',
        data: {
          cardId: cardId,
          nextReviewTime: card.nextReviewTime
        }
      })

      return {
        success: true,
        message: '复习时间已变更，已重新安排提醒'
      }
    }

    // 新的授权机制：每次发送都使用独立授权，不需要检查用户订阅状态
    console.log('使用独立授权机制，直接尝试发送提醒')

    // 发送订阅消息（使用独立授权机制）
    const result = await cloud.openapi.subscribeMessage.send({
      touser: openid,
      templateId: 'lNNqgUJU3ITeDC9hlQosOO-a_bF4iF8EM_TpTF3Ap4c',
      page: `pages/card-review/card-review?id=${card._id}`,
      data: {
        time1: {
          value: now.toISOString().slice(0, 19).replace('T', ' ')
        },
        thing2: {
          value: '学习计划'
        },
        thing3: {
          value: `${card.title} 需要复习了`
        }
      }
    })

    console.log('延迟提醒发送成功')

    // 更新卡片提醒状态
    await db.collection('cards').doc(cardId).update({
      data: {
        reminderSent: true,
        reminderSentTime: db.serverDate()
      }
    })

    // 更新提醒记录状态
    try {
      await db.collection('reminders').where({
        cardId: cardId,
        status: 'scheduled'
      }).update({
        data: {
          status: 'sent',
          sentTime: db.serverDate()
        }
      })
      console.log('已更新提醒记录状态为已发送')
    } catch (error) {
      console.log('更新提醒记录状态失败:', error.message)
      // 不影响主要功能，继续执行
    }

    return {
      success: true,
      message: '延迟提醒发送成功',
      data: {
        cardId: cardId,
        cardTitle: card.title,
        msgid: result.msgid ? String(result.msgid) : null
      }
    }

  } catch (error) {
    console.error('发送延迟提醒失败', error)

    // 特殊处理43101错误（用户拒绝接收消息）
    if (error.errCode === 43101) {
      console.log('用户授权已过期，更新用户订阅状态')

      // 更新用户订阅状态为false，并设置授权过期标记
      try {
        await db.collection('users').where({
          openid: openid
        }).update({
          data: {
            subscribeMessageEnabled: false,
            lastAuthError: '授权已过期，需要重新授权',
            lastAuthErrorTime: db.serverDate(),
            authExpired: true  // 添加授权过期标记
          }
        })
        console.log('已更新用户订阅状态为false，并标记授权过期')
      } catch (updateUserError) {
        console.error('更新用户订阅状态失败', updateUserError)
      }

      // 清理该用户的所有待发送提醒
      try {
        await db.collection('reminders').where({
          _openid: openid,
          status: 'scheduled'
        }).update({
          data: {
            status: 'cancelled',
            cancelReason: '用户授权已过期',
            cancelTime: db.serverDate()
          }
        })
        console.log('已取消该用户的所有待发送提醒')
      } catch (cancelError) {
        console.error('取消提醒失败', cancelError)
      }
    }

    // 更新提醒记录状态为失败
    try {
      await db.collection('reminders').where({
        cardId: cardId,
        status: 'scheduled'
      }).update({
        data: {
          status: 'failed',
          failTime: db.serverDate(),
          error: error.message || String(error),
          errorCode: error.errCode || -1
        }
      })
    } catch (updateError) {
      console.error('更新提醒记录失败', updateError)
    }

    return {
      success: false,
      message: error.errCode === 43101 ? '用户授权已过期，需要重新授权' : '发送延迟提醒失败',
      error: error.message || String(error),
      errorCode: error.errCode || -1,
      needReauth: error.errCode === 43101
    }
  }
}
