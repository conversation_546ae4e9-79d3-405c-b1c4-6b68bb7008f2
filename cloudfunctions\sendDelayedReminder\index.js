// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
  traceUser: true
})

const db = cloud.database()

/**
 * 发送延迟的复习提醒
 * 重写版本：移除所有外部依赖，确保没有 timeUtils 引用
 * @param {Object} event - 云函数调用参数
 * @param {string} event.openid - 用户openid
 * @param {string} event.cardId - 卡片ID
 * @param {Object} context - 云函数上下文
 * @returns {Object} 发送结果
 */
exports.main = async (event, context) => {
  const { openid, cardId } = event

  try {
    console.log(`发送延迟提醒: 用户 ${openid}, 卡片 ${cardId}`)

    // 验证必要参数
    if (!openid || !cardId) {
      return {
        success: false,
        message: '缺少必要参数：openid 或 cardId'
      }
    }

    // 获取最新的卡片信息
    const cardResult = await db.collection('cards').doc(cardId).get()
    if (!cardResult.data) {
      console.log('卡片已被删除，取消发送提醒')

      // 清理提醒记录
      try {
        await db.collection('reminders').where({
          cardId: cardId,
          status: 'scheduled'
        }).remove()
      } catch (cleanupError) {
        console.log('清理提醒记录失败:', cleanupError.message)
      }

      return {
        success: true,
        message: '卡片已删除，提醒已取消'
      }
    }

    const card = cardResult.data

    // 检查卡片是否已归档
    if (card.archived || card.isArchived) {
      console.log('卡片已归档，取消发送提醒')

      // 清理提醒记录
      try {
        await db.collection('reminders').where({
          cardId: cardId,
          status: 'scheduled'
        }).remove()
      } catch (cleanupError) {
        console.log('清理提醒记录失败:', cleanupError.message)
      }

      return {
        success: true,
        message: '卡片已归档，提醒已取消'
      }
    }

    // 检查复习时间是否已经变更
    const now = new Date()
    const nextReviewTime = new Date(card.nextReviewTime)

    if (nextReviewTime > now) {
      console.log('复习时间已变更，跳过发送提醒')
      return {
        success: true,
        message: '复习时间已变更，跳过发送提醒'
      }
    }

    // 格式化当前时间
    const currentTime = now.toISOString().slice(0, 19).replace('T', ' ')

    // 截断卡片标题，确保不超过微信限制
    const cardTitle = card.title ? card.title.substring(0, 20) : '记忆卡片'

    console.log('开始发送订阅消息')

    // 发送订阅消息
    const result = await cloud.openapi.subscribeMessage.send({
      touser: openid,
      templateId: 'lNNqgUJU3ITeDC9hlQosOO-a_bF4iF8EM_TpTF3Ap4c',
      page: `pages/card-review/card-review?id=${card._id}`,
      data: {
        time1: {
          value: currentTime
        },
        thing2: {
          value: '学习计划'
        },
        thing3: {
          value: `${cardTitle} 需要复习了`
        }
      }
    })

    console.log('延迟提醒发送成功')

    // 并行更新卡片状态和提醒记录
    const updatePromises = [
      // 更新卡片提醒状态
      db.collection('cards').doc(cardId).update({
        data: {
          reminderSent: true,
          reminderSentTime: db.serverDate()
        }
      }),
      // 更新提醒记录状态
      db.collection('reminders').where({
        cardId: cardId,
        status: 'scheduled'
      }).update({
        data: {
          status: 'sent',
          sentTime: db.serverDate()
        }
      })
    ]

    await Promise.allSettled(updatePromises)
    console.log('已更新卡片和提醒记录状态')

    return {
      success: true,
      message: '延迟提醒发送成功',
      data: {
        cardId: cardId,
        cardTitle: cardTitle,
        msgid: result.msgid ? String(result.msgid) : null
      }
    }

  } catch (error) {
    console.error('发送延迟提醒失败', error)

    // 处理授权过期错误
    if (error.errCode === 43101 || error.errCode === 43104) {
      console.log('用户授权已过期，更新状态')

      try {
        // 并行执行：更新用户状态和取消提醒
        const cleanupPromises = [
          // 更新用户订阅状态
          db.collection('users').where({
            openid: openid
          }).update({
            data: {
              subscribeMessageEnabled: false,
              lastAuthError: '授权已过期，需要重新授权',
              lastAuthErrorTime: db.serverDate(),
              authExpired: true
            }
          }),
          // 取消该用户的所有待发送提醒
          db.collection('reminders').where({
            _openid: openid,
            status: 'scheduled'
          }).update({
            data: {
              status: 'cancelled',
              cancelReason: '用户授权已过期',
              cancelTime: db.serverDate()
            }
          })
        ]

        await Promise.allSettled(cleanupPromises)
        console.log('已处理授权过期相关清理工作')
      } catch (cleanupError) {
        console.error('清理工作失败', cleanupError)
      }
    } else {
      // 其他错误，只更新提醒记录状态
      try {
        await db.collection('reminders').where({
          cardId: cardId,
          status: 'scheduled'
        }).update({
          data: {
            status: 'failed',
            failTime: db.serverDate(),
            error: error.message || String(error),
            errorCode: error.errCode || -1
          }
        })
      } catch (updateError) {
        console.error('更新提醒记录失败', updateError)
      }
    }

    return {
      success: false,
      message: error.errCode === 43101 || error.errCode === 43104 ?
        '用户授权已过期，需要重新授权' : '发送延迟提醒失败',
      error: error.message || String(error),
      errorCode: error.errCode || -1,
      needReauth: error.errCode === 43101 || error.errCode === 43104
    }
  }
}
