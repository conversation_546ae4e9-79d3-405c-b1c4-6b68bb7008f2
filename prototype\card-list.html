<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡片列表页</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- TDesign风格的自定义配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0052d9', // TDesign主色调
                        success: '#00a870',
                        warning: '#ed7b2f',
                        error: '#e34d59',
                        'gray-1': '#f3f3f3',
                        'gray-2': '#eee',
                        'gray-3': '#e7e7e7',
                        'gray-4': '#dcdcdc',
                        'gray-5': '#c5c5c5',
                        'gray-6': '#a6a6a6',
                        'gray-7': '#8b8b8b',
                        'gray-8': '#5e5e5e',
                        'gray-9': '#3f3f3f',
                        'gray-10': '#2c2c2c',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f5f7fa;
        }
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 56px;
            background-color: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
            z-index: 100;
        }
        .card-list {
            margin-top: 66px;
            padding: 10px 16px;
            padding-bottom: 80px;
        }
        .card-item {
            background-color: #fff;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }
        .card-item:active {
            transform: scale(0.98);
        }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            background-color: #e7f1ff;
            color: #0052d9;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 8px;
        }
        .add-button {
            position: fixed;
            right: 20px;
            bottom: 20px;
            width: 56px;
            height: 56px;
            border-radius: 28px;
            background-color: #0052d9;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 4px 10px rgba(0, 82, 217, 0.3);
            z-index: 100;
        }
        .filter-dropdown {
            position: absolute;
            top: 56px;
            left: 0;
            width: 200px;
            background-color: #fff;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            z-index: 99;
            display: none;
        }
        .filter-item {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
        }
        .filter-item:last-child {
            border-bottom: none;
        }
        .filter-item.active {
            color: #0052d9;
            background-color: #f5f7ff;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header">
        <div class="flex items-center">
            <button id="filterBtn" class="flex items-center text-gray-8">
                <span>全部卡片</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1">
                    <path d="m6 9 6 6 6-6"/>
                </svg>
            </button>
            
            <!-- 标签筛选下拉菜单 -->
            <div id="filterDropdown" class="filter-dropdown">
                <div class="filter-item active">全部卡片</div>
                <div class="filter-item">英语词汇</div>
                <div class="filter-item">公式定理</div>
                <div class="filter-item">每日复盘</div>
                <div class="filter-item">工作笔记</div>
            </div>
        </div>
        <div class="text-lg font-medium">记忆卡片</div>
        <button class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-1">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"/>
                <path d="M12 16v-4"/>
                <path d="M12 8h.01"/>
            </svg>
        </button>
    </div>
    
    <!-- 卡片列表 -->
    <div class="card-list">
        <!-- 卡片项 1 -->
        <div class="card-item">
            <div class="flex justify-between items-start mb-2">
                <h3 class="text-lg font-medium">英语单词记忆技巧</h3>
                <span class="text-xs text-gray-6">2025-05-20</span>
            </div>
            <p class="text-gray-8 text-sm mb-3 line-clamp-2">通过词根词缀记忆法可以有效地扩大词汇量，例如"con-"表示"共同"，"-duct"表示"引导"，所以"conduct"就是"共同引导"...</p>
            <div class="flex items-center">
                <span class="tag">英语词汇</span>
                <span class="tag">学习方法</span>
            </div>
        </div>
        
        <!-- 卡片项 2 -->
        <div class="card-item">
            <div class="flex justify-between items-start mb-2">
                <h3 class="text-lg font-medium">微积分基本定理</h3>
                <span class="text-xs text-gray-6">2025-05-18</span>
            </div>
            <p class="text-gray-8 text-sm mb-3 line-clamp-2">如果函数F是连续函数f在区间[a,b]上的一个原函数，那么∫(a,b)f(x)dx = F(b) - F(a)。这是微积分中最重要的定理之一...</p>
            <div class="flex items-center">
                <span class="tag">公式定理</span>
                <span class="tag">数学</span>
            </div>
        </div>
        
        <!-- 卡片项 3 -->
        <div class="card-item">
            <div class="flex justify-between items-start mb-2">
                <h3 class="text-lg font-medium">项目管理五个过程组</h3>
                <span class="text-xs text-gray-6">2025-05-15</span>
            </div>
            <p class="text-gray-8 text-sm mb-3 line-clamp-2">项目管理包括启动、规划、执行、监控和收尾五个过程组。启动过程组定义新项目或现有项目的新阶段，规划过程组确立项目范围...</p>
            <div class="flex items-center">
                <span class="tag">工作笔记</span>
            </div>
        </div>
        
        <!-- 卡片项 4 -->
        <div class="card-item">
            <div class="flex justify-between items-start mb-2">
                <h3 class="text-lg font-medium">今日工作总结</h3>
                <span class="text-xs text-gray-6">2025-05-12</span>
            </div>
            <p class="text-gray-8 text-sm mb-3 line-clamp-2">1. 完成了用户需求分析报告；2. 与设计团队讨论了界面原型；3. 解决了两个关键bug；4. 明天需要准备周会演示...</p>
            <div class="flex items-center">
                <span class="tag">每日复盘</span>
                <span class="tag">工作笔记</span>
            </div>
        </div>
    </div>
    
    <!-- 添加按钮 -->
    <button class="add-button">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M12 5v14M5 12h14"/>
        </svg>
    </button>
    
    <script>
        // 标签筛选功能
        const filterBtn = document.getElementById('filterBtn');
        const filterDropdown = document.getElementById('filterDropdown');
        
        filterBtn.addEventListener('click', function() {
            if (filterDropdown.style.display === 'block') {
                filterDropdown.style.display = 'none';
            } else {
                filterDropdown.style.display = 'block';
            }
        });
        
        // 点击其他地方关闭下拉菜单
        document.addEventListener('click', function(event) {
            if (!filterBtn.contains(event.target) && !filterDropdown.contains(event.target)) {
                filterDropdown.style.display = 'none';
            }
        });
        
        // 选择标签筛选
        const filterItems = document.querySelectorAll('.filter-item');
        filterItems.forEach(item => {
            item.addEventListener('click', function() {
                filterItems.forEach(i => i.classList.remove('active'));
                this.classList.add('active');
                filterBtn.querySelector('span').textContent = this.textContent;
                filterDropdown.style.display = 'none';
                
                // 这里可以添加实际筛选逻辑
            });
        });
    </script>
</body>
</html>
