// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
  traceUser: true
})

const db = cloud.database()

/**
 * 测试云函数性能和功能
 * 用于验证修复后的云函数是否正常工作
 * @param {Object} event - 云函数调用参数
 * @param {string} event.testType - 测试类型 (reminders|schedule|database)
 * @param {Object} context - 云函数上下文
 * @returns {Object} 测试结果
 */
exports.main = async (event, context) => {
  const { testType = 'all' } = event
  const wxContext = cloud.getWXContext()
  
  try {
    console.log(`开始测试云函数，测试类型: ${testType}`)
    
    const results = {
      success: true,
      message: '测试完成',
      data: {
        testTime: new Date().toISOString(),
        testType: testType,
        results: []
      }
    }

    // 测试数据库连接
    if (testType === 'all' || testType === 'database') {
      try {
        const startTime = Date.now()
        
        // 测试各个集合的连接
        const collections = ['users', 'cards', 'tags', 'reminders']
        const dbResults = []
        
        for (const collectionName of collections) {
          try {
            const result = await db.collection(collectionName).limit(1).get()
            dbResults.push({
              collection: collectionName,
              status: 'success',
              count: result.data.length,
              message: '连接正常'
            })
          } catch (error) {
            dbResults.push({
              collection: collectionName,
              status: 'error',
              message: error.message,
              errCode: error.errCode
            })
          }
        }
        
        const endTime = Date.now()
        results.data.results.push({
          test: 'database',
          status: 'completed',
          duration: `${endTime - startTime}ms`,
          results: dbResults
        })
        
      } catch (error) {
        results.data.results.push({
          test: 'database',
          status: 'error',
          message: error.message
        })
      }
    }

    // 测试提醒处理功能
    if (testType === 'all' || testType === 'reminders') {
      try {
        const startTime = Date.now()
        
        // 检查待处理的提醒
        const now = new Date()
        const remindersResult = await db.collection('reminders').where({
          status: 'scheduled'
        }).get()
        
        const dueReminders = remindersResult.data.filter(r => 
          new Date(r.reminderTime) <= now
        )
        
        const endTime = Date.now()
        results.data.results.push({
          test: 'reminders',
          status: 'completed',
          duration: `${endTime - startTime}ms`,
          data: {
            totalScheduled: remindersResult.data.length,
            dueCount: dueReminders.length,
            nextDue: remindersResult.data.length > 0 ? 
              Math.min(...remindersResult.data.map(r => new Date(r.reminderTime).getTime())) : null
          }
        })
        
      } catch (error) {
        results.data.results.push({
          test: 'reminders',
          status: 'error',
          message: error.message
        })
      }
    }

    // 测试调度功能
    if (testType === 'all' || testType === 'schedule') {
      try {
        const startTime = Date.now()
        
        // 模拟调度测试（不实际创建提醒）
        const testCardId = 'test-card-id'
        const testTime = new Date(Date.now() + 60000).toISOString() // 1分钟后
        
        // 这里只是验证参数处理逻辑，不实际调用
        const scheduleTest = {
          cardId: testCardId,
          nextReviewTime: testTime,
          openid: wxContext.OPENID || 'test-openid'
        }
        
        const isValidSchedule = scheduleTest.cardId && 
                               scheduleTest.nextReviewTime && 
                               scheduleTest.openid &&
                               !isNaN(new Date(scheduleTest.nextReviewTime).getTime())
        
        const endTime = Date.now()
        results.data.results.push({
          test: 'schedule',
          status: 'completed',
          duration: `${endTime - startTime}ms`,
          data: {
            parameterValidation: isValidSchedule ? 'passed' : 'failed',
            testParameters: scheduleTest
          }
        })
        
      } catch (error) {
        results.data.results.push({
          test: 'schedule',
          status: 'error',
          message: error.message
        })
      }
    }

    console.log('测试完成')
    return results

  } catch (error) {
    console.error('测试失败', error)
    return {
      success: false,
      message: '测试失败',
      error: error.message || String(error),
      errorCode: error.errCode || -1
    }
  }
}
