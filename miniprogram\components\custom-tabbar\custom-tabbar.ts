Component({
  /**
   * 组件的属性列表
   */
  properties: {
    value: {
      type: String,
      value: 'card-list'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 处理TabBar切换事件
     */
    onChange(e: any) {
      const value = e.detail.value;
      const currentPages = getCurrentPages();
      const currentPage = currentPages[currentPages.length - 1];
      const currentRoute = currentPage.route;

      // 避免重复跳转到当前页面
      if ((value === 'card-list' && currentRoute === 'pages/card-list/card-list') ||
          (value === 'profile' && currentRoute === 'pages/profile/profile')) {
        return;
      }

      switch (value) {
        case 'card-list':
          wx.redirectTo({
            url: '/pages/card-list/card-list'
          });
          break;
        case 'profile':
          wx.redirectTo({
            url: '/pages/profile/profile'
          });
          break;
      }
    }
  }
});
