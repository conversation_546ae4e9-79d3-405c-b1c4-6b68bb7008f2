// app.ts
/**
 * 记忆卡片小程序应用入口
 * 初始化云开发环境和全局数据
 */
App<IAppOption>({
  globalData: {
    userInfo: null,
    isLoggedIn: false
  },

  /**
   * 小程序启动时初始化
   */
  onLaunch() {
    // 初始化云开发环境
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        env: 'cloud1-6gx9namae523f863', // 用户提供的云环境ID
        traceUser: true
      })
    }

    // 检查用户登录状态
    this.checkLoginStatus()
  },

  /**
   * 检查用户登录状态
   */
  checkLoginStatus() {
    wx.getStorage({
      key: 'userInfo',
      success: (res) => {
        this.globalData.userInfo = res.data
        this.globalData.isLoggedIn = true

        // 如果有用户信息，尝试从云端同步最新信息
        this.syncUserInfo()
      },
      fail: () => {
        // 未找到登录信息，用户未登录
        this.globalData.isLoggedIn = false
      }
    })
  },

  /**
   * 同步用户信息
   */
  syncUserInfo() {
    if (!this.globalData.isLoggedIn || !this.globalData.userInfo) {
      return
    }

    // 静默调用登录云函数获取最新用户信息
    wx.cloud.callFunction({
      name: 'login',
      data: {},
      success: (result: any) => {
        if (result.result && result.result.success) {
          const loginData = result.result.data;
          const userInfo = {
            openid: loginData.openid,
            unionid: loginData.unionid,
            appid: loginData.appid,
            nickName: loginData.userData?.nickName || '微信用户',
            avatarUrl: loginData.userData?.avatarUrl || '',
            _id: loginData.userData?._id,
            isFirstLogin: false // 已经登录过了
          };

          // 更新本地存储和全局数据
          wx.setStorage({
            key: 'userInfo',
            data: userInfo
          });
          this.globalData.userInfo = userInfo;
        }
      },
      fail: (err: any) => {
        console.log('同步用户信息失败', err);
        // 同步失败不影响正常使用
      }
    });
  }
})