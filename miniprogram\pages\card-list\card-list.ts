/**
 * 卡片列表页面逻辑
 * 显示用户的所有记忆卡片，支持标签筛选和添加新卡片
 */
Page({
  data: {
    cards: [] as any[],
    tagOptions: [] as any[],
    activeTagId: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.checkLoginStatus();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.checkLoginStatus();
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const app = getApp<IAppOption>();

    // 检查全局登录状态
    if (!app.globalData.isLoggedIn) {
      // 尝试从本地存储获取用户信息
      wx.getStorage({
        key: 'userInfo',
        success: (res) => {
          app.globalData.userInfo = res.data;
          app.globalData.isLoggedIn = true;
          this.loadData();
        },
        fail: () => {
          // 未登录，跳转到登录页
          wx.redirectTo({
            url: '/pages/login/login'
          });
        }
      });
    } else {
      this.loadData();
    }
  },

  /**
   * 加载页面数据
   */
  loadData() {
    this.loadTags();
    this.loadCards();
  },

  /**
   * 加载用户的所有标签
   */
  loadTags() {
    // 显示加载中
    wx.showLoading({ title: '加载中' });

    // 调用云函数获取标签列表
    wx.cloud.callFunction({
      name: 'getTags',
      success: (res: any) => {
        console.log('获取标签成功', res);

        if (res.result && res.result.success) {
          const tags = res.result.data || [];

          // 构建下拉菜单选项
          const options = [
            { label: '全部卡片', value: '' },
            ...tags.map((tag: any) => ({
              label: tag.name,
              value: tag._id
            }))
          ];

          this.setData({
            tagOptions: options
          });
        } else {
          console.error('获取标签失败', res.result);
          this.setData({
            tagOptions: [{ label: '全部卡片', value: '' }]
          });
        }
      },
      fail: (err: any) => {
        console.error('获取标签失败', err);
        wx.showToast({
          title: '获取标签失败',
          icon: 'error'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 加载用户的记忆卡片列表
   */
  loadCards() {
    // 显示加载中
    wx.showLoading({ title: '加载中' });

    // 构建查询参数
    const params: any = {};
    if (this.data.activeTagId) {
      params.tagId = this.data.activeTagId;
    }

    // 调用云函数获取卡片列表
    wx.cloud.callFunction({
      name: 'getCards',
      data: params,
      success: (res: any) => {
        console.log('获取卡片成功', res);

        if (res.result && res.result.success) {
          const cards = res.result.data || [];

          // 处理卡片数据，格式化日期和内容摘要
          const formattedCards = cards.map((card: any) => {
            const reviewData = this.calculateReviewData(card);
            return {
              ...card,
              id: card._id, // 确保有id字段
              createTime: this.formatDate(card.createTime),
              content: this.truncateContent(card.content),
              ...reviewData
            };
          });

          // 按照复习时间排序
          const sortedCards = this.sortCardsByReviewTime(formattedCards);

          this.setData({
            cards: sortedCards
          });
        } else {
          console.error('获取卡片失败', res.result);
          this.setData({
            cards: []
          });
          wx.showToast({
            title: res.result?.message || '获取卡片失败',
            icon: 'error'
          });
        }
      },
      fail: (err: any) => {
        console.error('获取卡片失败', err);
        wx.showToast({
          title: '获取卡片失败',
          icon: 'error'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 处理标签筛选变化
   */
  onTagChange(e: any) {
    const tagId = e.detail.value;
    this.setData({
      activeTagId: tagId
    });

    // 重新加载卡片列表
    this.loadCards();
  },

  /**
   * 处理卡片点击事件
   */
  onCardTap(e: any) {
    const cardId = e.currentTarget.dataset.id;
    const cardIndex = e.currentTarget.dataset.index;
    const card = this.data.cards[cardIndex];

    // 显示操作选择弹窗
    this.showCardActionSheet(cardId, card);
  },

  /**
   * 显示卡片操作选择弹窗
   */
  showCardActionSheet(cardId: string, card: any) {
    const itemList = ['编辑卡片', '复习卡片'];

    wx.showActionSheet({
      itemList: itemList,
      success: (res) => {
        if (res.tapIndex === 0) {
          // 编辑卡片
          wx.navigateTo({
            url: `/pages/card-edit/card-edit?id=${cardId}`
          });
        } else if (res.tapIndex === 1) {
          // 复习卡片
          wx.navigateTo({
            url: `/pages/card-review/card-review?id=${cardId}`
          });
        }
      }
    });
  },

  /**
   * 处理添加卡片按钮点击事件
   */
  onAddCard() {
    wx.navigateTo({
      url: '/pages/card-edit/card-edit'
    });
  },

  /**
   * 格式化日期为 YYYY-MM-DD 格式
   */
  formatDate(timestamp: number | string) {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  },

  /**
   * 截断内容，生成摘要
   */
  truncateContent(content: string) {
    if (!content) return '';

    // 限制在50个字符以内
    if (content.length <= 50) {
      return content;
    }

    return content.substring(0, 50) + '...';
  },

  /**
   * 获取用户设置的复习间隔
   */
  getUserReviewIntervals() {
    try {
      const settings = wx.getStorageSync('ebbinghausSettings');
      if (settings) {
        const reviewIntervals = {
          review1: 5,
          review2: 30,
          review3: 12 * 60,
          review4: 24 * 60,
          review5: 2 * 24 * 60,
          review6: 4 * 24 * 60,
          review7: 7 * 24 * 60,
          review8: 15 * 24 * 60,
          review9: 30 * 24 * 60,
          review10: 60 * 24 * 60,
          review11: 90 * 24 * 60,
          review12: 120 * 24 * 60
        };

        const enabledIntervals = [];
        for (const key in settings) {
          if (settings[key] && reviewIntervals[key]) {
            enabledIntervals.push(reviewIntervals[key]);
          }
        }

        return enabledIntervals.length > 0 ? enabledIntervals : [5, 30, 12 * 60, 24 * 60, 2 * 24 * 60, 7 * 24 * 60, 30 * 24 * 60];
      }
    } catch (error) {
      console.error('获取复习设置失败', error);
    }

    // 默认间隔（分钟）
    return [5, 30, 12 * 60, 24 * 60, 2 * 24 * 60, 7 * 24 * 60, 30 * 24 * 60];
  },

  /**
   * 格式化复习时间显示
   */
  formatReviewTimeDisplay(diffTime: number): string {
    if (diffTime < 0) {
      return '需要复习';
    }

    const diffMinutes = Math.ceil(diffTime / (1000 * 60));
    const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffMinutes < 60) {
      if (diffMinutes <= 1) {
        return '即将复习';
      }
      return `${diffMinutes}分钟后复习`;
    } else if (diffHours < 24) {
      return `${diffHours}小时后复习`;
    } else if (diffDays === 1) {
      return '明天复习';
    } else {
      return `${diffDays}天后复习`;
    }
  },

  /**
   * 按照复习时间排序卡片
   * 规则：
   * 1. 需要复习的卡片排在前面，按照过期时间长短排序（越早过期的越靠前）
   * 2. 不需要复习的卡片排在后面，按照距离复习时间的远近排序（越近的越靠前）
   */
  sortCardsByReviewTime(cards: any[]): any[] {
    const now = new Date().getTime();

    return cards.sort((a, b) => {
      const aReviewTime = new Date(a.nextReviewTime).getTime();
      const bReviewTime = new Date(b.nextReviewTime).getTime();

      const aIsOverdue = aReviewTime <= now;
      const bIsOverdue = bReviewTime <= now;

      // 如果一个需要复习，一个不需要，需要复习的排在前面
      if (aIsOverdue && !bIsOverdue) {
        return -1;
      }
      if (!aIsOverdue && bIsOverdue) {
        return 1;
      }

      // 如果都需要复习，越早过期的排在越前面
      if (aIsOverdue && bIsOverdue) {
        return aReviewTime - bReviewTime;
      }

      // 如果都不需要复习，距离复习时间越近的排在越前面
      if (!aIsOverdue && !bIsOverdue) {
        return aReviewTime - bReviewTime;
      }

      return 0;
    });
  },

  /**
   * 计算复习相关数据
   */
  calculateReviewData(card: any) {
    const now = new Date();

    // 获取用户设置的复习间隔
    const reviewIntervals = this.getUserReviewIntervals();

    // 如果有下次复习时间，使用它；否则根据用户设置计算第一次复习时间
    let nextReviewTime;
    if (card.nextReviewTime) {
      nextReviewTime = new Date(card.nextReviewTime);
    } else {
      // 新卡片，第一次复习时间根据用户设置
      nextReviewTime = new Date(card.createTime);
      const firstReviewInterval = reviewIntervals[0] || 5; // 默认5分钟
      nextReviewTime.setTime(nextReviewTime.getTime() + firstReviewInterval * 60 * 1000);
    }

    // 当前复习轮次（从0开始）
    const currentRound = card.currentRound || 0;

    // 复习总次数
    const totalReviewCount = card.totalReviewCount || 0;

    // 判断是否需要复习（当前时间 >= 下次复习时间）
    const needReview = now >= nextReviewTime;

    // 计算时间差
    const diffTime = nextReviewTime.getTime() - now.getTime();

    // 格式化显示时间
    const timeUntilReview = this.formatReviewTimeDisplay(diffTime);

    // 当前间隔（分钟）
    const currentIntervalMinutes = reviewIntervals[Math.min(currentRound, reviewIntervals.length - 1)] || reviewIntervals[reviewIntervals.length - 1];

    return {
      needReview,
      currentRound,
      totalReviewCount,
      timeUntilReview,
      currentIntervalMinutes,
      nextReviewTime: nextReviewTime.toISOString()
    };
  }
});
