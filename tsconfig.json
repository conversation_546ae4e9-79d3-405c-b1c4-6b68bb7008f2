{"compilerOptions": {"strictNullChecks": true, "noImplicitAny": true, "module": "CommonJS", "target": "ES2020", "allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "noImplicitThis": true, "noImplicitReturns": true, "alwaysStrict": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "strict": true, "strictPropertyInitialization": true, "lib": ["ES2020"], "typeRoots": ["./typings"], "paths": {"tdesign-miniprogram/*": ["./miniprogram/miniprogram_npm/tdesign-miniprogram/*"]}}, "include": ["./**/*.ts"], "exclude": ["node_modules"]}