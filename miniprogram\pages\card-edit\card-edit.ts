/**
 * 卡片编辑页面逻辑
 * 支持新建和编辑记忆卡片，包括标题、内容、图片、标签和提醒设置
 */
Page({
  data: {
    cardId: '', // 卡片ID，新建时为空
    isEdit: false, // 是否为编辑模式
    submitting: false, // 是否正在提交
    cardData: {
      title: '',
      content: '',
      imageUrl: '',
      imageFiles: [] as any[]
    },
    tags: [] as any[], // 所有标签列表
    selectedTags: [] as string[], // 已选标签ID列表
    selectedTagsData: [] as any[], // 已选标签的详细数据
    availableTags: [] as any[] // 可选标签（未选中的）
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options: any) {
    // 如果传入了卡片ID，则为编辑模式
    if (options.id) {
      this.setData({
        cardId: options.id,
        isEdit: true
      });
      this.loadCardData(options.id);
    }

    // 加载标签列表
    this.loadTags();
  },

  /**
   * 加载卡片数据
   */
  loadCardData(cardId: string) {
    wx.showLoading({ title: '加载中' });

    wx.cloud.callFunction({
      name: 'getCardDetail',
      data: { cardId },
      success: (res: any) => {
        console.log('获取卡片详情成功', res);

        if (res.result && res.result.success && res.result.data) {
          const cardData = res.result.data;

          // 处理图片文件列表格式
          const imageFiles = cardData.imageUrl ? [{
            url: cardData.imageUrl,
            name: 'image.png',
            type: 'image'
          }] : [];

          // 设置卡片数据
          this.setData({
            cardData: {
              title: cardData.title || '',
              content: cardData.content || '',
              imageUrl: cardData.imageUrl || '',
              imageFiles: imageFiles
            },
            selectedTags: cardData.tagIds || []
          }, () => {
            // 卡片数据加载完成后，更新标签分类
            this.updateTagsClassification();
          });
        } else {
          wx.showToast({
            title: '卡片数据加载失败',
            icon: 'error'
          });
        }
      },
      fail: (err: any) => {
        console.error('获取卡片详情失败', err);
        wx.showToast({
          title: '获取卡片详情失败',
          icon: 'error'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 加载标签列表
   */
  loadTags() {
    wx.cloud.callFunction({
      name: 'getTags',
      success: (res: any) => {
        console.log('获取标签成功', res);

        if (res.result && res.result.success) {
          const tags = res.result.data || [];

          const formattedTags = tags.map((tag: any) => ({
            id: tag._id,
            name: tag.name,
            color: tag.color || '#0052d9'
          }));

          this.setData({
            tags: formattedTags
          }, () => {
            // 标签加载完成后，更新标签分类
            this.updateTagsClassification();
          });
        } else {
          console.error('获取标签失败', res.result);
          this.setData({
            tags: []
          });
        }
      },
      fail: (err: any) => {
        console.error('获取标签失败', err);
        wx.showToast({
          title: '获取标签失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 更新标签分类（已选和可选）
   */
  updateTagsClassification() {
    const { tags, selectedTags } = this.data;

    const selectedTagsData = tags.filter((tag: any) => selectedTags.includes(tag.id));
    const availableTags = tags.filter((tag: any) => !selectedTags.includes(tag.id));

    this.setData({
      selectedTagsData,
      availableTags
    });
  },

  /**
   * 选择图片
   */
  onChooseImage() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        this.uploadImageToCloud(tempFilePath);
      },
      fail: (err) => {
        console.error('选择图片失败', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 上传图片到云存储
   */
  uploadImageToCloud(filePath: string) {
    wx.showLoading({ title: '上传中' });

    const fileName = `card_image_${Date.now()}.jpg`;
    wx.cloud.uploadFile({
      cloudPath: `images/${fileName}`,
      filePath: filePath,
      success: (uploadRes) => {
        const imageUrl = uploadRes.fileID;

        this.setData({
          'cardData.imageUrl': imageUrl
        });

        wx.showToast({
          title: '图片上传成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('图片上传失败', err);
        wx.showToast({
          title: '图片上传失败',
          icon: 'error'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 处理图片移除事件
   */
  onImageRemove() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张图片吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            'cardData.imageUrl': ''
          });
          wx.showToast({
            title: '图片已删除',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * @description 处理标题输入变化
   * @param e 事件对象
   */
  onTitleChange(e: any) {
    this.setData({
      'cardData.title': e.detail.value
    });
  },

  /**
   * @description 处理内容输入变化
   * @param e 事件对象
   */
  onContentChange(e: any) {
    this.setData({
      'cardData.content': e.detail.value
    });
  },

  /**
   * 选择标签
   */
  onSelectTag(e: any) {
    const tagId = e.currentTarget.dataset.id;
    const selectedTags = [...this.data.selectedTags];

    if (!selectedTags.includes(tagId)) {
      selectedTags.push(tagId);
      this.setData({ selectedTags }, () => {
        this.updateTagsClassification();
      });
    }
  },

  /**
   * 移除标签
   */
  onRemoveTag(e: any) {
    const tagId = e.currentTarget.dataset.id;
    const selectedTags = this.data.selectedTags.filter(id => id !== tagId);

    this.setData({ selectedTags }, () => {
      this.updateTagsClassification();
    });
  },

  /**
   * 添加新标签
   */
  onAddTag() {
    wx.showModal({
      title: '添加标签',
      content: '请输入标签名称',
      editable: true,
      placeholderText: '标签名称',
      success: (res) => {
        if (res.confirm && res.content && res.content.trim()) {
          this.createNewTag(res.content.trim());
        }
      }
    });
  },

  /**
   * 创建新标签
   */
  createNewTag(tagName: string) {
    // 检查标签是否已存在
    const existingTag = this.data.tags.find((tag: any) => tag.name === tagName);
    if (existingTag) {
      wx.showToast({
        title: '标签已存在',
        icon: 'error'
      });
      return;
    }

    wx.showLoading({ title: '创建中' });

    wx.cloud.callFunction({
      name: 'saveTag',
      data: {
        name: tagName,
        color: '#0052d9' // 默认颜色
      },
      success: (res: any) => {
        if (res.result && res.result.success) {
          wx.showToast({
            title: '标签创建成功',
            icon: 'success'
          });

          // 重新加载标签列表
          this.loadTags();
        } else {
          wx.showToast({
            title: res.result?.message || '创建失败',
            icon: 'error'
          });
        }
      },
      fail: (err: any) => {
        console.error('创建标签失败', err);
        wx.showToast({
          title: '创建失败',
          icon: 'error'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 处理管理标签按钮点击事件
   */
  onManageTags() {
    wx.navigateTo({
      url: '/pages/tag-manage/tag-manage'
    });
  },



  /**
   * 获取用户设置的第一次复习间隔
   */
  getFirstReviewInterval() {
    try {
      const settings = wx.getStorageSync('ebbinghausSettings');
      if (settings) {
        const reviewIntervals = {
          review1: 5,
          review2: 30,
          review3: 12 * 60,
          review4: 24 * 60,
          review5: 2 * 24 * 60,
          review6: 4 * 24 * 60,
          review7: 7 * 24 * 60,
          review8: 15 * 24 * 60,
          review9: 30 * 24 * 60,
          review10: 60 * 24 * 60,
          review11: 90 * 24 * 60,
          review12: 120 * 24 * 60
        };

        // 找到第一个启用的复习间隔
        for (const key in settings) {
          if (settings[key] && reviewIntervals[key]) {
            return reviewIntervals[key];
          }
        }
      }
    } catch (error) {
      console.error('获取复习设置失败', error);
    }

    // 默认5分钟
    return 5;
  },

  /**
   * @description 处理保存按钮点击事件
   */
  handleSaveButtonTap() {
    console.log('[handleSaveButtonTap] Method invoked.');

    const title = this.data.cardData.title;
    const content = this.data.cardData.content;

    // 表单验证
    if (!title || !title.trim()) {
      wx.showToast({
        title: '请输入卡片标题',
        icon: 'error'
      });
      return;
    }

    this.setData({ submitting: true });

    const cardDataToSave = {
      title: title.trim(),
      content: content ? content.trim() : '',
      imageUrl: this.data.cardData.imageUrl,
      tagIds: this.data.selectedTags
    };

    // 如果是新建卡片，添加第一次复习间隔
    if (!this.data.isEdit) {
      cardDataToSave.firstReviewInterval = this.getFirstReviewInterval();
    }

    const functionName = 'saveCard';
    const params = this.data.isEdit ? { cardId: this.data.cardId, ...cardDataToSave } : cardDataToSave;

    console.log('[handleSaveButtonTap] Calling cloud function:', functionName, 'with params:', params);

    wx.cloud.callFunction({
      name: functionName,
      data: params,
      success: (res: any) => {
        console.log('[handleSaveButtonTap] Cloud function success:', res);

        if (res.result && res.result.success) {
          // 如果是新建卡片，立即请求第一次提醒授权
          if (!this.data.isEdit) {
            this.requestFirstReminderAuth(res.result.cardId);
          } else {
            wx.showToast({
              title: '保存成功',
              icon: 'success'
            });

            setTimeout(() => {
              wx.navigateBack();
            }, 1500);
          }
        } else {
          wx.showToast({
            title: res.result?.message || '保存失败',
            icon: 'error'
          });
        }
      },
      fail: (err: any) => {
        console.error('[handleSaveButtonTap] Cloud function fail:', err);
        wx.showToast({
          title: '保存失败，请重试',
          icon: 'error'
        });
      },
      complete: () => {
        console.log('[handleSaveButtonTap] Cloud function complete.');
        this.setData({ submitting: false });
      }
    });
  },

  /**
   * 处理删除卡片按钮点击事件
   */
  onDeleteCard() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张卡片吗？删除后无法恢复。',
      confirmColor: '#e34d59',
      success: (res) => {
        if (res.confirm) {
          this.deleteCard();
        }
      }
    });
  },

  /**
   * 处理归档卡片按钮点击事件
   */
  onArchiveCard() {
    wx.showModal({
      title: '确认归档',
      content: '确定要归档这张卡片吗？归档后卡片将不再出现在复习计划中。',
      success: (res) => {
        if (res.confirm) {
          this.archiveCard();
        }
      }
    });
  },

  /**
   * 请求第一次提醒授权
   */
  requestFirstReminderAuth(cardId: string) {
    const templateId = 'lNNqgUJU3ITeDC9hlQosOO-a_bF4iF8EM_TpTF3Ap4c';

    wx.showModal({
      title: '开启提醒',
      content: '卡片创建成功！是否开启第一次复习提醒？开启后系统会在复习时间到达时通知您。',
      confirmText: '开启提醒',
      cancelText: '暂不开启',
      success: (modalRes) => {
        if (modalRes.confirm) {
          // 请求订阅消息授权
          wx.requestSubscribeMessage({
            tmplIds: [templateId],
            success: (subscribeRes: any) => {
              console.log('第一次提醒授权结果', subscribeRes);

              if (subscribeRes[templateId] === 'accept') {
                wx.showToast({
                  title: '卡片创建成功，已开启提醒！',
                  icon: 'success',
                  duration: 2000
                });
              } else {
                wx.showToast({
                  title: '卡片创建成功',
                  icon: 'success',
                  duration: 2000
                });
              }

              setTimeout(() => {
                wx.navigateBack();
              }, 2000);
            },
            fail: (err: any) => {
              console.error('第一次提醒授权失败', err);
              wx.showToast({
                title: '卡片创建成功',
                icon: 'success',
                duration: 2000
              });

              setTimeout(() => {
                wx.navigateBack();
              }, 2000);
            }
          });
        } else {
          // 用户选择暂不开启
          wx.showToast({
            title: '卡片创建成功',
            icon: 'success',
            duration: 2000
          });

          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
        }
      }
    });
  },

  /**
   * 归档卡片
   */
  archiveCard() {
    wx.showLoading({ title: '归档中' });

    wx.cloud.callFunction({
      name: 'archiveCard',
      data: { cardId: this.data.cardId },
      success: (res: any) => {
        console.log('归档卡片成功', res);

        if (res.result && res.result.success) {
          wx.showToast({
            title: '归档成功',
            icon: 'success'
          });

          // 返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: res.result?.message || '归档失败',
            icon: 'error'
          });
        }
      },
      fail: (err: any) => {
        console.error('归档卡片失败', err);
        wx.showToast({
          title: '归档失败，请重试',
          icon: 'error'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 删除卡片
   */
  deleteCard() {
    wx.showLoading({ title: '删除中' });

    wx.cloud.callFunction({
      name: 'deleteCard',
      data: { cardId: this.data.cardId },
      success: (res: any) => {
        console.log('删除卡片成功', res);

        if (res.result && res.result.success) {
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });

          // 返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: res.result?.message || '删除失败',
            icon: 'error'
          });
        }
      },
      fail: (err: any) => {
        console.error('删除卡片失败', err);
        wx.showToast({
          title: '删除失败，请重试',
          icon: 'error'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 格式化日期时间为 YYYY-MM-DD HH:MM 格式
   */
  formatDateTime(timestamp: number | string) {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }
});
