.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.content {
  background-color: white;
  border-radius: 12rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.result {
  margin-top: 40rpx;
  padding: 30rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.result-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
}

.result-status.success {
  background-color: #52c41a;
}

.result-status.error {
  background-color: #ff4d4f;
}

.result-message {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.result-details {
  border-top: 1rpx solid #e8e8e8;
  padding-top: 20rpx;
}

.details-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.collection-name {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
}

.collection-status {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: white;
  margin-right: 16rpx;
}

.collection-status.exists {
  background-color: #52c41a;
}

.collection-status.created {
  background-color: #1890ff;
}

.collection-status.missing {
  background-color: #faad14;
}

.collection-status.error {
  background-color: #ff4d4f;
}

.collection-message {
  flex: 2;
  font-size: 24rpx;
  color: #666;
}

.tips {
  margin-top: 40rpx;
  padding: 30rpx;
  background-color: #e6f7ff;
  border-radius: 8rpx;
  border-left: 4rpx solid #1890ff;
}

.tips-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 12rpx;
}

.tips-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}
