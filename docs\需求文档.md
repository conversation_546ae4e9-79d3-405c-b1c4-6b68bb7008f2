# 记忆卡片小程序需求分析

**年级：** 2022级
**专业：** 信息管理与信息系统
**作者：** 张天颖

## 目录

* [记忆卡片小程序需求分析](#记忆卡片小程序需求分析)
* [一、项目概述](#一项目概述)
    * [1.1 项目背景](#11-项目背景)
    * [1.2 项目目标](#12-项目目标)
    * [1.3 用户特征](#13-用户特征)
    * [1.4 运行环境](#14-运行环境)
    * [1.5 项目范围和限制](#15-项目范围和限制)
* [二、功能需求](#二功能需求)
    * [2.1 卡片管理](#21-卡片管理)
    * [2.2 标签管理](#22-标签管理)
    * [2.3 用户登录与身份管理](#23-用户登录与身份管理)
    * [2.4 数据存储与云端同步](#24-数据存储与云端同步)
    * [2.5 复习提醒功能](#25-复习提醒功能)
    * [2.6 卡片归档功能](#26-卡片归档功能)
* [三、系统结构设计](#三系统结构设计)
* [四、数据流图](#四数据流图)
* [五、数据字典](#五数据字典)
* [六、数据建模](#六数据建模)
* [七、后续可能的扩展](#七后续可能的扩展)

---

## 一、项目概述

### 1.1 项目背景

随着生活节奏的加快和知识更新频率的提升，人们面临着大量需要记忆和学习的内容。传统的笔记或记忆方法零散且效率不高，难以及时巩固所学知识。微信小程序凭借其庞大的用户基础和便捷的即开即用特点，为用户提供了一个随时随地记录和复习的理想平台。
记忆卡片小程序旨在面向各类有记忆学习需求的用户，通过卡片化的方式帮助用户有效地整理和强化记忆内容。

### 1.2 项目目标

本项目的目标是在微信小程序平台上构建一款轻便高效的记忆辅助工具。用户可以创建自定义的记忆卡片，记录需要记忆的知识点或事项，并利用碎片时间随时复习。系统通过标签分类、艾宾浩斯遗忘曲线复习计划和智能提醒等功能，帮助用户对记忆内容进行有序管理和科学化巩固，从而提高学习效率，减少遗忘。最终，小程序将为用户打造一个个性化的移动记忆库，满足学习、工作中各种场景的记忆需求。

### 1.3 用户特征

本小程序的目标用户广泛，可能包括学生、职场人士以及任何需要记忆知识的人群。用户的技术水平不一，有的用户对新应用较为熟悉，也有用户缺乏技术经验。针对这一特征，小程序在设计上强调界面简洁、操作直观。用户无需具备专业技术背景即可使用主要功能，系统应提供清晰的引导和友好的交互，确保不同水平的用户都能顺利创建和浏览记忆卡片。良好的用户体验将提高用户持续使用的积极性。

### 1.4 运行环境

记忆卡片小程序将运行于微信平台的小程序环境。用户需要使用微信应用进入小程序，无需单独安装App。系统利用微信提供的云开发环境作为后端支持，使用云数据库存储用户数据。前端在微信小程序框架下开发，适配各种手机屏幕尺寸，确保在安卓和iOS微信中均能正常运行。微信平台提供的一键登录授权和订阅消息能力将集成到本小程序中，实现免注册登录和到期提醒推送。由于依赖微信生态，本小程序的使用需在有网络连接的手机微信环境下进行。

### 1.5 项目范围和限制

本项目专注于微信小程序平台的实现，暂时不考虑其他平台的兼容。用户通过微信即可使用全部功能，无需额外账户体系。数据分析统计功能暂时不在本项目范围内，小程序不涉及对用户记忆数据的复杂统计或可视化分析。此外，小程序主要功能围绕个人记忆管理，不包含社交分享、多人协作等扩展功能。项目重点在于实现卡片的记录、分类、复习和提醒功能，保证核心功能的易用性和稳定性。

## 二、功能需求

本节根据用户需求列出记忆卡片小程序应具备的主要功能模块，包括卡片管理、标签管理、用户登录、数据同步、复习提醒和归档管理等方面。

### 2.1 卡片管理

用户能够新建记忆卡片来记录需要记忆的内容。每张卡片包含**标题**和**内容**两个部分，其中内容支持富文本形式，允许用户输入文字段落，并可插入一张相关图片（例如知识点截图、示意图等）。用户可以编辑修改已有卡片的标题或内容，亦可删除不再需要的卡片。为了方便浏览，小程序提供卡片列表视图展示所有卡片的标题摘要，点击可查看卡片详情。

**批量操作功能**：系统支持批量删除卡片功能，用户可以通过长按选择多张卡片进行批量删除操作，提高管理效率。

**卡片排序**：卡片列表按照复习时间进行排序，优先显示需要复习的卡片，帮助用户合理安排复习计划。

卡片管理功能应确保操作简单，一两个步骤即可完成增删改查操作，并在用户执行敏感操作（如删除）时给予确认提示，避免误操作。

### 2.2 标签管理

为了便于分类和检索，用户可以为每张记忆卡片添加**标签**。标签相当于卡片的分类标记，用户可自定义标签名称（如"英语词汇"、"公式定理"、"每日复盘"等）。小程序支持用户新建、编辑和删除标签，以及在创建或编辑卡片时给卡片指定一个或多个标签。通过标签，用户能够按类别查看相关的卡片集合。

**标签操作方式**：标签管理采用长按操作进行删除和重命名，支持批量删除标签功能。

标签管理应支持一个卡片多标签，以及一个标签下包含多张卡片的多对多关系。界面上应提供标签列表或下拉菜单，方便用户按标签筛选卡片，从而有针对性地进行分类记忆。标签功能的实现有助于用户整理知识结构，快速定位特定主题的记忆内容。

### 2.3 用户登录与身份管理

小程序采用微信一键登录方式进行用户身份管理。用户首次进入小程序时，可直接使用微信账号授权登录，无需单独注册账户。系统通过微信提供的OpenID来唯一标识用户。登录过程对用户来说是无感的，授权后小程序即可获取用户基本标识用于数据隔离。

**持久登录**：用户登录成功后，系统会保持登录状态，无需在每次使用时重复登录。基于此，每个用户的记忆卡片和标签数据将私有保存，不会混淆到他人。由于不涉及密码管理，身份认证主要依赖微信平台的安全机制，开发中需确保正确校验微信登录态。

用户登录后，小程序可选择性获取用户昵称头像用于界面显示，但不强制要求用户提供额外信息。总之，用户管理模块完全依托微信生态完成认证授权，降低用户使用门槛。

### 2.4 数据存储与云端同步

记忆卡片小程序的所有数据需要同步保存到云端，以保证用户更换设备或小程序更新后数据不丢失。系统将使用微信小程序的云开发数据库来存储用户的卡片、标签等数据。每当用户新建、编辑或删除卡片（或标签）时，客户端操作会通过云函数或API将变更提交到云端数据库保存。相应地，用户打开小程序时，客户端会从云端拉取最新的卡片和标签数据进行展示。

数据同步需保证**实时性**和**可靠性**：用户本地的操作应及时更新到服务器，服务器数据更新后其他终端登录同一账户时能够获取最新状态。考虑到网络不稳定情况，小程序应具备一定的离线缓存能力，确保在无网络时用户仍能浏览已有卡片，并在网络恢复后同步离线期间的操作。数据存储应注意基本的安全和权限控制，只允许经过验证的用户访问其自身的数据。通过云端同步，记忆卡片小程序可以提供连续的使用体验，避免因为设备切换或意外卸载而造成数据遗失。

### 2.5 复习提醒功能

为了帮助用户巩固记忆，小程序提供基于**艾宾浩斯遗忘曲线**的智能复习提醒功能。系统根据科学的记忆遗忘规律，自动为每张卡片安排多轮复习时间点，包括5分钟、30分钟、12小时、1天、2天、7天、30天等间隔。用户可以在全局设置中自定义启用哪些复习时间点，这些设置将应用于所有新创建的卡片。

**复习效果反馈系统**：用户在复习时可以选择记忆效果（完全记住、部分记住、完全忘记），系统会根据反馈调整下次复习时间：
- **完全记住**：进入下一个复习轮次
- **部分记住**：重复当前轮次的复习间隔  
- **完全忘记**：回退到上一轮次的复习间隔

**实时提醒推送**：系统通过微信订阅消息服务，在卡片到达复习时间时立即发送提醒通知。用户点击通知可直接打开小程序进入复习界面。提醒功能需要用户授权订阅消息，系统会在适当时机请求用户授权。

**提前复习支持**：用户可以提前复习尚未到期的卡片，提前复习同样会影响复习计划和轮次进度。系统支持按复习时间排序显示卡片，优先显示需要复习的卡片。

### 2.6 卡片归档功能

**自动归档机制**：当卡片完成所有设定的复习轮次且用户选择"完全记住"时，系统会自动将卡片归档，表示该知识点已充分掌握。

**归档管理**：归档的卡片可以在专门的归档页面查看和管理，用户可以选择恢复归档的卡片重新开始复习计划。归档功能帮助用户区分已掌握和正在学习的内容，保持学习界面的整洁。

**手动归档**：除了自动归档外，用户也可以在卡片编辑页面手动归档不再需要复习的卡片。

## 三、系统结构设计

上述功能由前端小程序和后端云服务共同实现。用户通过微信客户端打开记忆卡片小程序，在前端界面进行卡片的创建、浏览等操作。小程序前端（运行在用户的微信中）负责呈现界面并响应用户交互，当用户保存卡片或查询数据时，前端会调用微信提供的**云函数接口**与后台交互。

后台使用微信云开发的数据库存储数据，包括用户信息、卡片内容、标签分类、复习记录和提醒设置等。所有数据读写都通过安全的API在小程序和云端之间同步。系统采用微信一键登录机制，前端获取用户的**登录凭证**后传递给微信平台，由平台校验并返回用户唯一标识给小程序后端，实现免注册的用户管理。

此外，小程序借助微信平台的**订阅消息服务**来安排提醒推送：云函数在卡片到达复习时间时触发消息，由微信服务器将通知发送给用户的微信。系统的整体架构中，各组件协同工作，实现数据的可靠存储和功能的顺畅运作。

## 四、数据流图

系统内部划分为五个主要功能处理模块：**1.卡片管理处理**、**2.标签管理处理**、**3.用户登录处理**、**4.提醒处理**、**5.归档管理处理**。

主要数据流包括：
- 用户登录验证流程
- 卡片的增删改查操作
- 标签的管理和筛选
- 复习提醒的设置和触发
- 卡片的归档和恢复

数据存储包括**卡片数据库**、**标签数据库**、**用户数据库**和**提醒记录数据库**，存放了用户所有记忆卡片的详细信息、标签分类、复习记录和提醒计划等。

## 五、数据字典

### 5.1 主要数据实体

1. **用户（User）**：存储用户基本信息，以微信OpenID作为主键
2. **卡片（Card）**：存储记忆卡片信息，包含复习状态和轮次信息
3. **标签（Tag）**：存储用户自定义标签
4. **提醒记录（Reminder）**：存储复习提醒的调度信息

### 5.2 关键字段说明

**卡片表新增字段**：
- `currentRound`：当前复习轮次
- `totalReviewCount`：总复习次数
- `nextReviewTime`：下次复习时间
- `lastReviewTime`：上次复习时间
- `reviewResult`：最近一次复习结果
- `archived`：是否已归档
- `archivedTime`：归档时间

## 六、数据建模

### 6.1 E-R 图

**用户**、**卡片**、**标签**、**提醒记录**是系统中的四类核心实体：
- 用户与卡片是一对多关系
- 卡片与标签是多对多关系
- 卡片与提醒记录是一对多关系
- 用户与标签是一对多关系

### 6.2 关系模式说明

* **用户（User）表**：用户ID（主键，微信OpenID），昵称，创建时间
* **卡片（Card）表**：卡片ID（主键），标题，内容，图片路径，用户ID（外键），复习相关字段，归档状态
* **标签（Tag）表**：标签ID（主键），名称，用户ID（外键）
* **提醒记录（Reminder）表**：提醒ID（主键），卡片ID（外键），提醒时间，状态

## 七、后续可能的扩展

1. 复习策略的多种可选择方案
2. 调用AI大模型API帮助更加科学地复习或者分析复习效率，提供建议等
3. 数据统计和可视化分析功能
4. 社交分享和协作学习功能
5. 多平台同步支持
