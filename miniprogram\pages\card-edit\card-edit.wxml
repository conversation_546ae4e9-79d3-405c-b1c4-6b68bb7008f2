<view class="card-edit-container">
  <form id="cardForm">
    <!-- 标题输入 -->
    <view class="form-item">
      <text class="form-label">标题</text>
      <t-input
        name="title"
        value="{{cardData.title}}"
        bind:change="onTitleChange"
        placeholder="请输入卡片标题"
        maxlength="50"
        borderless
        class="form-input"
      />
    </view>

    <!-- 内容输入 -->
    <view class="form-item">
      <text class="form-label">内容</text>
      <t-textarea
        name="content"
        value="{{cardData.content}}"
        bind:change="onContentChange"
        placeholder="请输入卡片内容"
        maxlength="1000"
        disableDefaultPadding="{{true}}"
        autosize
        class="form-textarea"
      />
    </view>

    <!-- 图片上传 -->
    <view class="form-item">
      <text class="form-label">图片</text>
      <view class="image-upload-container">
        <view wx:if="{{cardData.imageUrl}}" class="image-preview">
          <image src="{{cardData.imageUrl}}" mode="aspectFit" class="preview-image" />
          <view class="image-actions">
            <t-button theme="light" size="small" bindtap="onImageRemove" icon="delete">删除</t-button>
          </view>
        </view>
        <view wx:else class="upload-placeholder">
          <t-button theme="light" size="large" block bindtap="onChooseImage" icon="add">
            选择图片
          </t-button>
        </view>
      </view>
    </view>

    <!-- 标签选择 -->
    <view class="form-item">
      <view class="form-label-row">
        <text class="form-label">标签</text>
        <t-button theme="light" size="small" bindtap="onAddTag" icon="add">添加标签</t-button>
      </view>
      <view class="tags-container">
        <view class="selected-tags" wx:if="{{selectedTags.length > 0}}">
          <text class="tags-label">已选标签：</text>
          <view class="tag-list">
            <view
              wx:for="{{selectedTagsData}}"
              wx:key="id"
              class="tag-item selected-tag"
              data-id="{{item.id}}"
              bindtap="onRemoveTag"
            >
              <text>{{item.name}}</text>
              <t-icon name="close" size="24rpx" class="remove-icon" />
            </view>
          </view>
        </view>

        <view class="available-tags" wx:if="{{availableTags.length > 0}}">
          <text class="tags-label">可选标签：</text>
          <view class="tag-list">
            <view
              wx:for="{{availableTags}}"
              wx:key="id"
              class="tag-item available-tag"
              data-id="{{item.id}}"
              bindtap="onSelectTag"
            >
              <text>{{item.name}}</text>
              <t-icon name="add" size="24rpx" class="add-icon" />
            </view>
          </view>
        </view>

        <view wx:if="{{tags.length === 0}}" class="no-tags">
          <text>暂无标签，点击"添加标签"创建新标签</text>
        </view>
      </view>
    </view>



    <!-- 保存按钮 -->
    <view class="form-actions">
      <t-button theme="primary" size="large" block bindtap="handleSaveButtonTap" loading="{{submitting}}">
        保存
      </t-button>

      <t-button wx:if="{{isEdit}}" theme="warning" size="large" block bindtap="onArchiveCard" class="archive-btn">
        归档卡片
      </t-button>

      <t-button wx:if="{{isEdit}}" theme="danger" size="large" block bindtap="onDeleteCard" class="delete-btn">
        删除卡片
      </t-button>
    </view>
  </form>
</view>
