// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
  traceUser: true
})

const db = cloud.database()

/**
 * 处理到期提醒的定时任务云函数
 * 每5分钟执行一次，检查并发送到期的复习提醒
 * 优化版本：无限制处理所有到期提醒，使用并行处理提高效率
 * @param {Object} event - 云函数调用参数
 * @param {Object} context - 云函数上下文
 * @returns {Object} 处理结果
 */
exports.main = async (event, context) => {
  try {
    console.log('开始处理到期提醒...')

    const now = new Date()
    console.log(`当前时间: ${now.toISOString()}`)

    // 查找所有已经到期的提醒（无限制）
    const remindersResult = await db.collection('reminders').where({
      status: 'scheduled',
      reminderTime: db.command.lte(now)
    }).get()

    console.log(`找到 ${remindersResult.data.length} 条需要处理的提醒`)

    if (remindersResult.data.length === 0) {
      return {
        success: true,
        message: '没有需要处理的提醒',
        data: {
          totalReminders: 0,
          results: [],
          processTime: new Date().toISOString()
        }
      }
    }

    const results = []

    // 并行处理所有到期提醒，使用 Promise.allSettled 确保所有提醒都被处理
    const promises = remindersResult.data.map(async (reminder) => {
      try {
        console.log(`发送提醒: 卡片 ${reminder.cardId}`)

        // 发送提醒
        const sendResult = await cloud.callFunction({
          name: 'sendDelayedReminder',
          data: {
            openid: reminder._openid,
            cardId: reminder.cardId
          }
        })

        // 更新提醒状态
        await db.collection('reminders').doc(reminder._id).update({
          data: {
            status: sendResult.result?.success ? 'sent' : 'failed',
            processTime: db.serverDate(),
            result: sendResult.result?.message || '处理完成'
          }
        })

        return {
          cardId: reminder.cardId,
          status: sendResult.result?.success ? 'sent' : 'failed',
          message: sendResult.result?.message || '处理完成'
        }

      } catch (error) {
        console.error(`处理提醒失败: 卡片 ${reminder.cardId}`, error)

        // 更新提醒状态为错误
        try {
          await db.collection('reminders').doc(reminder._id).update({
            data: {
              status: 'error',
              processTime: db.serverDate(),
              error: error.message || String(error)
            }
          })
        } catch (updateError) {
          console.error('更新提醒状态失败', updateError)
        }

        return {
          cardId: reminder.cardId,
          status: 'error',
          message: error.message || String(error)
        }
      }
    })

    // 等待所有提醒处理完成
    const settledResults = await Promise.allSettled(promises)

    settledResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        results.push(result.value)
      } else {
        results.push({
          cardId: remindersResult.data[index].cardId,
          status: 'error',
          message: result.reason?.message || '处理失败'
        })
      }
    })

    console.log('提醒处理完成')

    return {
      success: true,
      message: '提醒处理完成',
      data: {
        totalReminders: remindersResult.data.length,
        results: results,
        processTime: new Date().toISOString()
      }
    }

  } catch (error) {
    console.error('处理提醒失败', error)
    return {
      success: false,
      message: '处理提醒失败',
      error: error.message || String(error),
      errorCode: error.errCode || -1
    }
  }
}
