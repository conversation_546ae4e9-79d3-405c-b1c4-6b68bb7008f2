// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
  traceUser: true
})

const db = cloud.database()

/**
 * 处理到期提醒的定时任务云函数
 * 每分钟执行一次，检查并发送到期的复习提醒
 * @param {Object} event - 云函数调用参数
 * @param {Object} context - 云函数上下文
 * @returns {Object} 处理结果
 */
exports.main = async (event, context) => {
  try {
    console.log('开始处理到期提醒...')

    const now = new Date()
    console.log(`当前时间: ${now.toISOString()}`)

    // 查找已经到期的提醒（简单直接）
    const remindersResult = await db.collection('reminders').where({
      status: 'scheduled',
      reminderTime: db.command.lte(now)
    }).get()

    console.log(`找到 ${remindersResult.data.length} 条需要处理的提醒`)

    const results = []

    // 简单直接：处理所有到期的提醒
    for (const reminder of remindersResult.data) {
      try {
        console.log(`发送提醒: 卡片 ${reminder.cardId}`)

        // 直接发送提醒，不做复杂判断
        const sendResult = await cloud.callFunction({
          name: 'sendDelayedReminder',
          data: {
            openid: reminder._openid,
            cardId: reminder.cardId
          }
        })

        if (sendResult.result && sendResult.result.success) {
          console.log(`卡片 ${reminder.cardId} 提醒发送成功`)
          results.push({
            cardId: reminder.cardId,
            status: 'sent',
            message: '提醒发送成功'
          })
        } else {
          console.error(`卡片 ${reminder.cardId} 提醒发送失败:`, sendResult.result?.message)
          results.push({
            cardId: reminder.cardId,
            status: 'failed',
            message: sendResult.result?.message || '发送失败'
          })
        }

      } catch (error) {
        console.error(`处理提醒失败: 卡片 ${reminder.cardId}`, error)
        results.push({
          cardId: reminder.cardId,
          status: 'error',
          message: error.message || String(error)
        })
      }
    }

    console.log('提醒处理完成')

    return {
      success: true,
      message: '提醒处理完成',
      data: {
        totalReminders: remindersResult.data.length,
        results: results,
        processTime: new Date().toISOString()
      }
    }

  } catch (error) {
    console.error('处理提醒失败', error)
    return {
      success: false,
      message: '处理提醒失败',
      error: error.message || String(error),
      errorCode: error.errCode || -1
    }
  }
}
