/**
 * 记忆卡片小程序全局样式
 */

/* 页面通用样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  background-color: #f5f7fa;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
}

/* 容器样式 */
.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* 清除浮动 */
.clearfix:after {
  content: "";
  display: block;
  clear: both;
}

/* 文本溢出省略号 */
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 多行文本溢出省略号 */
.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 常用间距 */
.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-30 { margin-top: 30rpx; }
.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-30 { margin-bottom: 30rpx; }
.ml-10 { margin-left: 10rpx; }
.mr-10 { margin-right: 10rpx; }

/* 常用颜色 */
.text-primary { color: #0052d9; }
.text-success { color: #00a870; }
.text-warning { color: #ed7b2f; }
.text-error { color: #e34d59; }
.text-gray { color: #8d8d8d; }

/* 常用按钮样式覆盖 */
.btn-block {
  width: 100%;
  margin-left: 0;
  margin-right: 0;
  padding-left: 0;
  padding-right: 0;
}
