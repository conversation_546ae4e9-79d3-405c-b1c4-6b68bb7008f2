<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡片编辑页</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- TDesign风格的自定义配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0052d9', // TDesign主色调
                        success: '#00a870',
                        warning: '#ed7b2f',
                        error: '#e34d59',
                        'gray-1': '#f3f3f3',
                        'gray-2': '#eee',
                        'gray-3': '#e7e7e7',
                        'gray-4': '#dcdcdc',
                        'gray-5': '#c5c5c5',
                        'gray-6': '#a6a6a6',
                        'gray-7': '#8b8b8b',
                        'gray-8': '#5e5e5e',
                        'gray-9': '#3f3f3f',
                        'gray-10': '#2c2c2c',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #fff;
        }
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 56px;
            background-color: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
            z-index: 100;
        }
        .edit-container {
            margin-top: 66px;
            padding: 10px 16px;
            padding-bottom: 80px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #5e5e5e;
        }
        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #dcdcdc;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        .form-input:focus {
            border-color: #0052d9;
            outline: none;
        }
        .form-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #dcdcdc;
            border-radius: 6px;
            font-size: 16px;
            min-height: 120px;
            resize: vertical;
            transition: border-color 0.3s;
        }
        .form-textarea:focus {
            border-color: #0052d9;
            outline: none;
        }
        .image-upload {
            width: 100%;
            height: 120px;
            border: 1px dashed #dcdcdc;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .image-upload:hover {
            border-color: #0052d9;
        }
        .tag-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 8px;
        }
        .tag {
            display: flex;
            align-items: center;
            padding: 6px 10px;
            background-color: #e7f1ff;
            color: #0052d9;
            border-radius: 4px;
            font-size: 14px;
        }
        .tag-add {
            display: flex;
            align-items: center;
            padding: 6px 10px;
            background-color: #f3f3f3;
            color: #5e5e5e;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
        }
        .reminder-box {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background-color: #f5f7fa;
            border-radius: 6px;
            cursor: pointer;
        }
        .save-button {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 16px;
            background-color: #fff;
            box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.08);
            z-index: 100;
        }
        .btn-primary {
            width: 100%;
            height: 48px;
            background-color: #0052d9;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn-primary:hover {
            background-color: #0045b6;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header">
        <button class="flex items-center text-gray-8">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m15 18-6-6 6-6"/>
            </svg>
            <span class="ml-1">返回</span>
        </button>
        <div class="text-lg font-medium">编辑卡片</div>
        <button class="w-8 h-8 flex items-center justify-center text-error">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M3 6h18"></path>
                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                <line x1="10" y1="11" x2="10" y2="17"></line>
                <line x1="14" y1="11" x2="14" y2="17"></line>
            </svg>
        </button>
    </div>
    
    <!-- 编辑表单 -->
    <div class="edit-container">
        <!-- 标题输入 -->
        <div class="form-group">
            <label class="form-label">标题</label>
            <input type="text" class="form-input" placeholder="请输入卡片标题" value="英语单词记忆技巧">
        </div>
        
        <!-- 内容输入 -->
        <div class="form-group">
            <label class="form-label">内容</label>
            <textarea class="form-textarea" placeholder="请输入卡片内容">通过词根词缀记忆法可以有效地扩大词汇量，例如"con-"表示"共同"，"-duct"表示"引导"，所以"conduct"就是"共同引导"。

记忆单词时，可以将相关单词组成一个故事或场景，这样更容易记住。

使用间隔重复法进行复习，可以根据艾宾浩斯遗忘曲线安排复习时间。</textarea>
        </div>
        
        <!-- 图片上传 -->
        <div class="form-group">
            <label class="form-label">图片</label>
            <div class="image-upload">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#8b8b8b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                    <circle cx="8.5" cy="8.5" r="1.5"></circle>
                    <polyline points="21 15 16 10 5 21"></polyline>
                </svg>
                <span class="mt-2 text-sm text-gray-7">点击上传图片</span>
            </div>
        </div>
        
        <!-- 标签选择 -->
        <div class="form-group">
            <label class="form-label">标签</label>
            <div class="tag-container">
                <div class="tag">
                    英语词汇
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </div>
                <div class="tag">
                    学习方法
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </div>
                <div class="tag-add">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1">
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                    添加标签
                </div>
            </div>
        </div>
        
        <!-- 提醒设置 -->
        <div class="form-group">
            <label class="form-label">提醒</label>
            <div class="reminder-box">
                <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#5e5e5e" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                        <path d="M10 2h4"></path>
                        <path d="M12 14v-4"></path>
                        <path d="M4 13a8 8 0 0 1 8-7 8 8 0 0 1 8 7v2a4 4 0 0 1-4 4H8a4 4 0 0 1-4-4z"></path>
                        <path d="M9 18h6"></path>
                    </svg>
                    <span>设置复习提醒</span>
                </div>
                <div class="text-primary">2025-05-27 20:00</div>
            </div>
        </div>
    </div>
    
    <!-- 保存按钮 -->
    <div class="save-button">
        <button class="btn-primary">保存</button>
    </div>
</body>
</html>
