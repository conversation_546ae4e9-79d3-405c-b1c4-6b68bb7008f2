// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 保存标签云函数
 * @param {Object} event - 云函数调用参数
 * @param {string} [event.tagId] - 标签ID，存在则为更新，不存在则为新建
 * @param {string} event.name - 标签名称
 * @param {string} [event.color] - 标签颜色，默认为蓝色
 * @param {Object} context - 云函数上下文
 * @returns {Object} 保存结果
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  try {
    const { tagId, name, color = '#0052d9' } = event
    
    if (!name) {
      return {
        success: false,
        message: '标签名称不能为空'
      }
    }
    
    // 构建标签数据
    const tagData = {
      name,
      color,
      updateTime: db.serverDate()
    }
    
    let result
    
    if (tagId) {
      // 更新现有标签
      result = await db.collection('tags').doc(tagId).update({
        data: tagData
      })
      
      return {
        success: true,
        message: '标签更新成功',
        tagId: tagId
      }
    } else {
      // 创建新标签
      tagData._openid = openid
      tagData.createTime = db.serverDate()
      
      // 检查是否已存在同名标签
      const existingTag = await db.collection('tags')
        .where({
          _openid: openid,
          name: name
        })
        .get()
      
      if (existingTag.data.length > 0) {
        return {
          success: false,
          message: '已存在同名标签'
        }
      }
      
      result = await db.collection('tags').add({
        data: tagData
      })
      
      return {
        success: true,
        message: '标签创建成功',
        tagId: result._id
      }
    }
  } catch (error) {
    console.error('保存标签失败', error)
    return {
      success: false,
      error: error
    }
  }
}
