/**
 * 艾宾浩斯遗忘曲线设置页面逻辑
 * 实现复习时间点的自定义设置
 */
Page({
  data: {
    settings: {
      review1: true,   // 5分钟
      review2: true,   // 30分钟
      review3: true,   // 12小时
      review4: true,   // 1天
      review5: true,   // 2天
      review6: false,  // 4天
      review7: true,   // 7天
      review8: false,  // 15天
      review9: true,   // 30天
      review10: false, // 60天
      review11: false, // 90天
      review12: false  // 120天
    } as Record<string, boolean>,
    // 复习时间间隔（分钟）
    reviewIntervals: {
      review1: 5,
      review2: 30,
      review3: 12 * 60,
      review4: 24 * 60,
      review5: 2 * 24 * 60,
      review6: 4 * 24 * 60,
      review7: 7 * 24 * 60,
      review8: 15 * 24 * 60,
      review9: 30 * 24 * 60,
      review10: 60 * 24 * 60,
      review11: 90 * 24 * 60,
      review12: 120 * 24 * 60
    } as Record<string, number>,
    saving: false // 是否正在保存
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.loadSettings();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '艾宾浩斯遗忘曲线设置'
    });
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 页面隐藏时自动保存设置
    this.saveSettings();
  },

  /**
   * 加载设置
   */
  loadSettings() {
    wx.getStorage({
      key: 'ebbinghausSettings',
      success: (res) => {
        this.setData({
          settings: res.data
        });
      },
      fail: () => {
        // 如果没有保存过设置，使用默认设置并保存
        this.saveSettings();
      }
    });
  },

  /**
   * 保存设置
   */
  saveSettings() {
    wx.setStorage({
      key: 'ebbinghausSettings',
      data: this.data.settings,
      success: () => {
        console.log('艾宾浩斯设置已保存');
      }
    });
  },

  /**
   * 处理开关切换事件
   */
  onSwitchChange(e: any) {
    const { key } = e.currentTarget.dataset;
    const value = e.detail.value;

    // 更新设置
    const settingsKey = `settings.${key}`;
    this.setData({
      [settingsKey]: value
    });

    // 保存设置
    this.saveSettings();

    // 提示用户
    wx.showToast({
      title: value ? '已开启' : '已关闭',
      icon: 'success'
    });
  },

  /**
   * 获取启用的复习时间点
   * 此方法可供其他页面调用
   */
  getEnabledReviewTimes() {
    const enabledTimes = [];

    for (const key in this.data.settings) {
      if (this.data.settings[key]) {
        enabledTimes.push({
          key: key,
          interval: this.data.reviewIntervals[key]
        });
      }
    }

    return enabledTimes;
  },

  /**
   * 保存艾宾浩斯遗忘曲线设置
   */
  saveEbbinghausReminders() {
    // 保存设置到本地存储
    this.saveSettings();

    wx.showToast({
      title: '设置已保存',
      icon: 'success'
    });

    // 返回上一页
    setTimeout(() => {
      wx.navigateBack();
    }, 1500);
  }
});
