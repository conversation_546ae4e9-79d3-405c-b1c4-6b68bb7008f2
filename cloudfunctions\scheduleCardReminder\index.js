// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
  traceUser: true
})

const db = cloud.database()

/**
 * 为单张卡片安排精确的复习提醒
 * 当卡片创建或复习时调用，设置下次复习的精确提醒时间
 * 优化版本：简化逻辑，提高执行效率
 * @param {Object} event - 云函数调用参数
 * @param {string} event.cardId - 卡片ID
 * @param {string} event.nextReviewTime - 下次复习时间
 * @param {Object} context - 云函数上下文
 * @returns {Object} 调度结果
 */
exports.main = async (event, context) => {
  const { cardId, nextReviewTime, openid: eventOpenid } = event
  const wxContext = cloud.getWXContext()
  const openid = eventOpenid || wxContext.OPENID

  try {
    console.log(`为卡片 ${cardId} 安排复习提醒，时间: ${nextReviewTime}，用户: ${openid}`)

    // 验证必要参数
    if (!cardId || !nextReviewTime) {
      return {
        success: false,
        message: '缺少必要参数：cardId 或 nextReviewTime'
      }
    }

    if (!openid) {
      return {
        success: false,
        message: '无法获取用户 openid'
      }
    }

    const reviewTime = new Date(nextReviewTime)
    const now = new Date()

    // 如果复习时间无效
    if (isNaN(reviewTime.getTime())) {
      return {
        success: false,
        message: '无效的复习时间格式'
      }
    }

    // 计算延迟时间
    const delay = reviewTime.getTime() - now.getTime()
    const delayHours = Math.round(delay / (1000 * 60 * 60))

    console.log(`安排复习提醒，延迟时间: ${delayHours} 小时`)

    // 如果复习时间已经过了，跳过安排提醒
    if (reviewTime <= now) {
      console.log('复习时间已过，跳过安排提醒')
      return {
        success: true,
        message: '复习时间已过，跳过安排提醒'
      }
    }

    // 并行执行：检查用户订阅状态和删除旧提醒
    const [userResult, deleteResult] = await Promise.allSettled([
      // 检查用户是否开启了订阅消息
      db.collection('users').where({ openid: openid }).get(),
      // 删除旧的提醒记录
      db.collection('reminders').where({
        cardId: cardId,
        status: 'scheduled'
      }).remove()
    ])

    // 检查用户订阅状态
    if (userResult.status === 'fulfilled') {
      const userData = userResult.value.data
      if (userData.length === 0 || !userData[0].subscribeMessageEnabled) {
        console.log(`用户 ${openid} 未开启订阅消息，跳过安排提醒`)
        return {
          success: true,
          message: '用户未开启订阅消息，跳过安排提醒'
        }
      }
    } else {
      console.log('检查用户订阅状态失败，继续安排提醒')
    }

    // 检查删除旧提醒的结果
    if (deleteResult.status === 'fulfilled') {
      console.log('已删除旧的提醒记录')
    } else {
      console.log('删除旧提醒记录失败:', deleteResult.reason?.message)
      if (deleteResult.reason?.errCode === -502005) {
        return {
          success: false,
          message: '提醒功能需要初始化，请先调用 initDatabase 云函数创建必要的数据库集合',
          error: 'reminders 集合不存在',
          needInit: true
        }
      }
    }

    // 创建新的提醒记录
    try {
      await db.collection('reminders').add({
        data: {
          _openid: openid,
          cardId: cardId,
          reminderTime: reviewTime,
          status: 'scheduled',
          createTime: db.serverDate(),
          type: 'review',
          delayHours: delayHours
        }
      })
      console.log('已创建新的提醒记录')
    } catch (error) {
      console.error('创建提醒记录失败:', error.message)
      if (error.errCode === -502005) {
        return {
          success: false,
          message: '提醒功能需要初始化，请先调用 initDatabase 云函数创建必要的数据库集合',
          error: 'reminders 集合不存在',
          needInit: true
        }
      }
      throw error
    }

    console.log(`提醒已记录到数据库，延迟时间: ${delayHours} 小时`)

    return {
      success: true,
      message: '提醒已记录，将由定时任务处理',
      data: {
        cardId: cardId,
        reminderTime: reviewTime.toISOString(),
        delayHours: delayHours,
        scheduleType: 'database'
      }
    }

  } catch (error) {
    console.error('安排复习提醒失败', error)
    return {
      success: false,
      message: '安排提醒失败',
      error: error.message || String(error)
    }
  }
}

/**
 * 立即发送提醒（已移除，不再使用）
 * 现在所有提醒都通过定时任务处理，确保严格按照艾宾浩斯时间发送
 */
