// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
  traceUser: true
})

const db = cloud.database()

/**
 * 为单张卡片安排精确的复习提醒
 * 当卡片创建或复习时调用，设置下次复习的精确提醒时间
 * @param {Object} event - 云函数调用参数
 * @param {string} event.cardId - 卡片ID
 * @param {string} event.nextReviewTime - 下次复习时间
 * @param {Object} context - 云函数上下文
 * @returns {Object} 调度结果
 */
exports.main = async (event, context) => {
  const { cardId, nextReviewTime, openid: eventOpenid } = event
  const wxContext = cloud.getWXContext()
  const openid = eventOpenid || wxContext.OPENID

  try {
    console.log(`为卡片 ${cardId} 安排复习提醒，时间: ${nextReviewTime}，用户: ${openid}`)

    // 验证必要参数
    if (!cardId || !nextReviewTime) {
      throw new Error('缺少必要参数：cardId 或 nextReviewTime')
    }

    if (!openid) {
      throw new Error('无法获取用户 openid')
    }

    // 检查用户是否开启了订阅消息
    const userResult = await db.collection('users').where({
      openid: openid
    }).get()

    if (userResult.data.length === 0 || !userResult.data[0].subscribeMessageEnabled) {
      console.log(`用户 ${openid} 未开启订阅消息，跳过安排提醒`)
      return {
        success: true,
        message: '用户未开启订阅消息，跳过安排提醒'
      }
    }

    // 获取卡片信息
    const cardResult = await db.collection('cards').doc(cardId).get()
    if (!cardResult.data) {
      throw new Error('卡片不存在')
    }

    const card = cardResult.data
    const reviewTime = new Date(nextReviewTime)
    const now = new Date()

    // 如果复习时间已经过了，立即发送提醒
    if (reviewTime <= now) {
      console.log('复习时间已到，立即发送提醒')
      return await sendImmediateReminder(openid, card)
    }

    // 计算延迟时间（毫秒）
    const delay = reviewTime.getTime() - now.getTime()
    const delayHours = Math.round(delay / (1000 * 60 * 60))

    console.log(`安排复习提醒，延迟时间: ${delayHours} 小时`)

    // 删除旧的提醒记录
    try {
      await db.collection('reminders').where({
        cardId: cardId,
        status: 'scheduled'
      }).remove()
      console.log('已删除旧的提醒记录')
    } catch (error) {
      console.log('删除旧提醒记录失败:', error.message)
      if (error.errCode === -502005) {
        console.log('reminders 集合不存在，需要先创建集合')
        return {
          success: false,
          message: '提醒功能需要初始化，请先调用 initDatabase 云函数创建必要的数据库集合',
          error: 'reminders 集合不存在',
          needInit: true
        }
      }
    }

    // 创建新的提醒记录
    try {
      await db.collection('reminders').add({
        data: {
          _openid: openid,
          cardId: cardId,
          reminderTime: reviewTime,
          status: 'scheduled',
          createTime: db.serverDate(),
          type: 'review',
          delayHours: delayHours
        }
      })
      console.log('已创建新的提醒记录')
    } catch (error) {
      console.error('创建提醒记录失败:', error.message)
      if (error.errCode === -502005) {
        return {
          success: false,
          message: '提醒功能需要初始化，请先调用 initDatabase 云函数创建必要的数据库集合',
          error: 'reminders 集合不存在',
          needInit: true
        }
      }
      throw error
    }

    // 所有提醒都存储到数据库，由定时任务处理
    // 这样确保严格按照艾宾浩斯复习时间发送提醒

    // 对于长期提醒（超过1小时），记录到数据库，需要定时任务处理
    console.log(`长期提醒已记录到数据库，延迟时间: ${delayHours} 小时`)

    return {
      success: true,
      message: '长期提醒已记录，需要定时任务处理',
      data: {
        cardId: cardId,
        reminderTime: reviewTime.toISOString(),
        delayHours: delayHours,
        scheduleType: 'database'
      }
    }

  } catch (error) {
    console.error('安排复习提醒失败', error)
    return {
      success: false,
      message: '安排提醒失败',
      error: error.message || String(error)
    }
  }
}

/**
 * 立即发送提醒
 */
async function sendImmediateReminder(openid, card) {
  try {
    const result = await cloud.openapi.subscribeMessage.send({
      touser: openid,
      templateId: 'lNNqgUJU3ITeDC9hlQosOO-a_bF4iF8EM_TpTF3Ap4c',
      page: `pages/card-review/card-review?id=${card._id}`,
      data: {
        time1: {
          value: new Date().toISOString().slice(0, 19).replace('T', ' ')
        },
        thing2: {
          value: '学习计划'
        },
        thing3: {
          value: `${card.title} 需要复习了`
        }
      }
    })

    console.log('立即发送提醒成功')

    // 更新卡片提醒状态
    await db.collection('cards').doc(card._id).update({
      data: {
        reminderSent: true,
        reminderSentTime: db.serverDate()
      }
    })

    return {
      success: true,
      message: '提醒已立即发送'
    }
  } catch (error) {
    console.error('立即发送提醒失败', error)
    return {
      success: false,
      message: '发送提醒失败',
      error: error.message || String(error)
    }
  }
}
