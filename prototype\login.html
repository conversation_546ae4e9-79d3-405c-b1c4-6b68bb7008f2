<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录页面</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- TDesign风格的自定义配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0052d9', // TDesign主色调
                        success: '#00a870',
                        warning: '#ed7b2f',
                        error: '#e34d59',
                        'gray-1': '#f3f3f3',
                        'gray-2': '#eee',
                        'gray-3': '#e7e7e7',
                        'gray-4': '#dcdcdc',
                        'gray-5': '#c5c5c5',
                        'gray-6': '#a6a6a6',
                        'gray-7': '#8b8b8b',
                        'gray-8': '#5e5e5e',
                        'gray-9': '#3f3f3f',
                        'gray-10': '#2c2c2c',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        .login-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 0 20px;
            background-color: #fff;
        }
        .login-logo {
            width: 120px;
            height: 120px;
            margin-bottom: 40px;
            background-color: #f5f5f5;
            border-radius: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .login-button {
            width: 100%;
            max-width: 300px;
            height: 50px;
            background-color: #07c160; /* 微信绿色 */
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 500;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }
        .login-button:hover {
            background-color: #06ad56;
        }
        .login-tip {
            margin-top: 15px;
            color: #8d8d8d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-logo">
            <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="#0052d9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
            </svg>
        </div>
        <h1 class="text-2xl font-bold mb-6 text-center">记忆卡片小程序</h1>
        <p class="text-gray-6 mb-8 text-center">高效记忆，随时随地复习</p>
        <button class="login-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                <path d="M12 2a10 10 0 0 1 10 10c0 5.5-5 14-10 14-5 0-10-8.5-10-14A10 10 0 0 1 12 2"></path>
                <circle cx="12" cy="10" r="3"></circle>
            </svg>
            微信一键登录
        </button>
        <p class="login-tip">使用微信账号快速登录</p>
    </div>
</body>
</html>
