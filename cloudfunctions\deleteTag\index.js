// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 删除标签云函数
 * @param {Object} event - 云函数调用参数
 * @param {string} event.tagId - 要删除的标签ID
 * @param {Object} context - 云函数上下文
 * @returns {Object} 删除结果
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  try {
    const { tagId } = event
    
    if (!tagId) {
      return {
        success: false,
        message: '缺少标签ID参数'
      }
    }
    
    // 先查询标签是否存在且属于当前用户
    const tag = await db.collection('tags').doc(tagId).get()
    
    if (!tag.data || tag.data._openid !== openid) {
      return {
        success: false,
        message: '标签不存在或无权限删除'
      }
    }
    
    // 查询是否有卡片使用了此标签
    const cards = await db.collection('cards')
      .where({
        _openid: openid,
        tagIds: tagId
      })
      .count()
    
    if (cards.total > 0) {
      // 从所有使用此标签的卡片中移除此标签
      const _ = db.command
      await db.collection('cards')
        .where({
          _openid: openid,
          tagIds: tagId
        })
        .update({
          data: {
            tagIds: _.pull(tagId)
          }
        })
    }
    
    // 删除标签
    await db.collection('tags').doc(tagId).remove()
    
    return {
      success: true,
      message: '标签删除成功'
    }
  } catch (error) {
    console.error('删除标签失败', error)
    return {
      success: false,
      error: error
    }
  }
}
