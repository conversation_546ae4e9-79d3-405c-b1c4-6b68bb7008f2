// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 获取标签列表云函数
 * @param {Object} event - 云函数调用参数
 * @param {Object} context - 云函数上下文
 * @returns {Object} 标签列表
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  try {
    // 查询当前用户的所有标签
    const tagsResult = await db.collection('tags')
      .where({
        _openid: openid
      })
      .orderBy('createTime', 'desc')
      .get()
    
    return {
      success: true,
      data: tagsResult.data
    }
  } catch (error) {
    console.error('获取标签列表失败', error)
    return {
      success: false,
      error: error
    }
  }
}
