// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 获取卡片详情云函数
 * @param {Object} event - 云函数调用参数
 * @param {string} event.cardId - 卡片ID
 * @param {Object} context - 云函数上下文
 * @returns {Object} 卡片详情
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  try {
    const { cardId } = event
    
    if (!cardId) {
      return {
        success: false,
        message: '缺少卡片ID参数'
      }
    }
    
    // 查询卡片详情
    const card = await db.collection('cards').doc(cardId).get()
    
    if (!card.data || card.data._openid !== openid) {
      return {
        success: false,
        message: '卡片不存在或无权限查看'
      }
    }
    
    // 获取卡片关联的标签信息
    const cardData = card.data
    const cardTags = []
    
    if (cardData.tagIds && cardData.tagIds.length > 0) {
      // 查询标签信息
      const tagsResult = await db.collection('tags')
        .where({
          _openid: openid,
          _id: db.command.in(cardData.tagIds)
        })
        .get()
      
      const tags = tagsResult.data
      
      cardData.tagIds.forEach(tagId => {
        const tag = tags.find(t => t._id === tagId)
        if (tag) {
          cardTags.push({
            id: tag._id,
            name: tag.name,
            color: tag.color || '#0052d9'
          })
        }
      })
    }
    
    return {
      success: true,
      data: {
        ...cardData,
        tags: cardTags
      }
    }
  } catch (error) {
    console.error('获取卡片详情失败', error)
    return {
      success: false,
      error: error
    }
  }
}
