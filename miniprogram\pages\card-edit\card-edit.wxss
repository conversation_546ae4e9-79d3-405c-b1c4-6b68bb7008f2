.card-edit-container {
  padding: 30rpx;
  background-color: #fff;
  min-height: 100vh;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #5e5e5e;
  margin-bottom: 16rpx;
}

.form-label-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.tag-actions {
  display: flex;
  gap: 16rpx;
}

.form-input {
  background-color: #f5f7fa;
  border-radius: 8rpx;
  padding: 20rpx !important;
}

.form-textarea {
  background-color: #f5f7fa;
  border-radius: 8rpx;
  padding: 20rpx !important;
  min-height: 240rpx;
}

.tags-container {
  margin-top: 20rpx;
}

.tags-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.tag-item {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.selected-tag {
  background-color: #e7f3ff;
  color: #0052d9;
  border: 2rpx solid #0052d9;
}

.selected-tag:hover {
  background-color: #cce7ff;
}

.available-tag {
  background-color: #f5f5f5;
  color: #666;
  border: 2rpx solid #e0e0e0;
}

.available-tag:hover {
  background-color: #e0e0e0;
  border-color: #0052d9;
}

.remove-icon, .add-icon {
  margin-left: 8rpx;
}

.no-tags {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 40rpx 0;
}

/* 图片上传样式 */
.image-upload-container {
  margin-top: 16rpx;
}

.image-preview {
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
  background-color: #f5f7fa;
}

.preview-image {
  width: 100%;
  height: 400rpx;
  border-radius: 8rpx;
}

.image-actions {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
}

.upload-placeholder {
  border: 2rpx dashed #e0e0e0;
  border-radius: 8rpx;
  padding: 60rpx 20rpx;
  text-align: center;
  background-color: #f9f9f9;
}

.reminder-cell {
  background-color: #f5f7fa !important;
  border-radius: 8rpx !important;
  margin-top: 16rpx;
}

.form-actions {
  margin-top: 60rpx;
  padding-bottom: 40rpx;
}

.archive-btn {
  margin-top: 30rpx;
}

.delete-btn {
  margin-top: 30rpx;
}
