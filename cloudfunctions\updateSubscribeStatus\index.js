// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
  traceUser: true
})

const db = cloud.database()

/**
 * 更新用户订阅消息状态云函数
 * @param {Object} event - 云函数调用参数
 * @param {boolean} event.enabled - 是否开启订阅消息
 * @param {Object} context - 云函数上下文
 * @returns {Object} 更新结果
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  const { enabled } = event
  
  try {
    console.log(`更新用户 ${openid} 订阅消息状态: ${enabled}`)
    
    // 查找用户记录
    const userResult = await db.collection('users').where({
      openid: openid
    }).get()
    
    if (userResult.data.length > 0) {
      // 用户存在，更新订阅状态
      await db.collection('users').where({
        openid: openid
      }).update({
        data: {
          subscribeMessageEnabled: enabled,
          subscribeUpdateTime: db.serverDate(),
          updateTime: db.serverDate()
        }
      })
    } else {
      // 用户不存在，创建用户记录
      await db.collection('users').add({
        data: {
          openid: openid,
          subscribeMessageEnabled: enabled,
          subscribeUpdateTime: db.serverDate(),
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        }
      })
    }
    
    console.log(`用户 ${openid} 订阅消息状态更新成功`)
    
    return {
      success: true,
      message: '订阅状态更新成功',
      data: {
        enabled: enabled
      }
    }
    
  } catch (error) {
    console.error('更新订阅消息状态失败', error)
    return {
      success: false,
      message: '更新失败',
      error: error.message || String(error)
    }
  }
}
