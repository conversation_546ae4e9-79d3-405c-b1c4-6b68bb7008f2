<view class="tag-manage-container">
  <!-- 顶部操作栏 -->
  <view class="top-actions">
    <!-- 添加标签表单 -->
    <view class="add-tag-form" wx:if="{{!isSelectionMode}}">
      <t-input
        value="{{newTagName}}"
        placeholder="请输入标签名称"
        bind:change="onInputChange"
        class="tag-input"
        borderless
      />
      <t-button theme="success" size="medium" bindtap="onAddTag" loading="{{addingTag}}">添加</t-button>
    </view>

    <!-- 选择模式操作栏 -->
    <view class="selection-actions" wx:if="{{isSelectionMode}}">
      <t-button theme="light" size="medium" bindtap="exitSelectionMode">取消</t-button>
      <text class="selection-count">已选择 {{selectedTags.length}} 个标签</text>
      <t-button theme="danger" size="medium" bindtap="batchDeleteTags" disabled="{{selectedTags.length === 0}}">删除</t-button>
    </view>

    <!-- 批量操作按钮 -->
    <view class="batch-actions" wx:if="{{!isSelectionMode && tags.length > 0}}">
      <t-button theme="light" size="small" bindtap="enterSelectionMode">批量管理</t-button>
    </view>
  </view>

  <!-- 标签列表 -->
  <view class="tag-list">
    <t-empty wx:if="{{tags.length === 0}}" description="暂无标签，请添加" />

    <block wx:for="{{tags}}" wx:key="id">
      <view
        class="tag-item-wrapper {{isSelectionMode ? 'selection-mode' : ''}}"
        bind:tap="onTagTap"
        bind:longpress="onTagLongPress"
        data-id="{{item.id}}"
        data-name="{{item.name}}"
      >
        <!-- 选择框 -->
        <view class="selection-checkbox" wx:if="{{isSelectionMode}}">
          <t-checkbox
            value="{{selectedTags.indexOf(item.id) > -1}}"
            data-id="{{item.id}}"
          />
        </view>

        <!-- 标签内容 -->
        <view class="tag-content {{selectedTags.indexOf(item.id) > -1 ? 'selected' : ''}}">
          <t-cell
            title="{{item.name}}"
            hover="{{!isSelectionMode}}"
            bordered
            t-class="tag-cell"
          />
        </view>
      </view>
    </block>
  </view>

  <!-- 编辑标签弹窗 -->
  <t-dialog
    visible="{{editDialogVisible}}"
    title="重命名标签"
    confirm-btn="保存"
    cancel-btn="取消"
    bind:confirm="onEditConfirm"
    bind:cancel="onEditCancel"
  >
    <t-input
      slot="content"
      value="{{editTagName}}"
      placeholder="请输入新的标签名称"
      bind:change="onEditInputChange"
      class="edit-tag-input"
      borderless
    />
  </t-dialog>
</view>
