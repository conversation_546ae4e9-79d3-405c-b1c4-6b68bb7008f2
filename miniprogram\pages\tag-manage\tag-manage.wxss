.tag-manage-container {
  padding: 30rpx;
  background-color: #fff;
  min-height: 100vh;
}

.top-actions {
  margin-bottom: 40rpx;
}

.add-tag-form {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.tag-input {
  flex: 1;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  padding: 0 20rpx !important;
}

.selection-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
}

.selection-count {
  font-size: 28rpx;
  color: #666;
}

.batch-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20rpx;
}

.tag-list {
  margin-top: 20rpx;
}

.tag-item-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  overflow: hidden;
  transition: all 0.3s ease;
}

.tag-item-wrapper.selection-mode {
  background-color: #f8f9fa;
}

.selection-checkbox {
  padding: 0 20rpx;
  display: flex;
  align-items: center;
}

.tag-content {
  flex: 1;
  transition: all 0.3s ease;
}

.tag-content.selected {
  background-color: #e7f3ff;
}

.tag-cell {
  background-color: transparent !important;
}

.edit-tag-input {
  margin-top: 20rpx;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  padding: 0 20rpx !important;
}
