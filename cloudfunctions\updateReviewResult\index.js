// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 更新卡片复习结果云函数
 * @param {Object} event - 云函数调用参数
 * @param {string} event.cardId - 卡片ID
 * @param {string} event.reviewResult - 复习结果 (forgot/normal/perfect)
 * @param {number} event.nextReviewTime - 下次复习时间戳
 * @param {number} event.currentRound - 当前复习轮次
 * @param {number} event.totalReviewCount - 总复习次数
 * @param {boolean} event.isEarlyReview - 是否为提前复习
 * @param {boolean} event.autoArchive - 是否自动归档
 * @param {Object} context - 云函数上下文
 * @returns {Object} 更新结果
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const {
    cardId,
    reviewResult,
    nextReviewTime,
    currentRound,
    totalReviewCount,
    isEarlyReview,
    autoArchive
  } = event

  try {
    // 验证参数
    if (!cardId) {
      return {
        success: false,
        message: '卡片ID不能为空'
      }
    }

    if (!reviewResult) {
      return {
        success: false,
        message: '复习结果不能为空'
      }
    }

    // 构建更新数据
    const updateData = {
      lastReviewTime: db.serverDate(),
      nextReviewTime: new Date(nextReviewTime),
      reviewResult: reviewResult,
      updateTime: db.serverDate(),
      reminderSent: false, // 重置提醒状态，允许下次复习时间到达时再次发送提醒
      reminderSentTime: null
    }

    // 提前复习和正常复习都会更新轮次和次数
    if (currentRound !== undefined) {
      updateData.currentRound = currentRound
    }
    if (totalReviewCount !== undefined) {
      updateData.totalReviewCount = totalReviewCount
    }

    // 如果需要自动归档
    if (autoArchive) {
      updateData.archived = true
      updateData.archivedTime = db.serverDate()
    }

    // 更新卡片的复习信息
    const updateResult = await db.collection('cards').doc(cardId).update({
      data: updateData
    })

    console.log('更新卡片复习结果成功', updateResult)

    // 如果卡片没有归档且有下次复习时间，安排复习提醒
    if (!autoArchive && updateData.nextReviewTime) {
      try {
        const wxContext = cloud.getWXContext()
        await cloud.callFunction({
          name: 'scheduleCardReminder',
          data: {
            cardId: cardId,
            nextReviewTime: updateData.nextReviewTime.toISOString(),
            openid: wxContext.OPENID
          }
        })
        console.log('已为复习后的卡片安排下次提醒')
      } catch (error) {
        console.error('安排下次复习提醒失败', error)
        // 不影响复习结果更新的成功
      }
    }

    return {
      success: true,
      message: autoArchive ? '复习结果更新成功，卡片已自动归档' : '复习结果更新成功',
      data: {
        updated: updateResult.stats.updated,
        isEarlyReview: isEarlyReview,
        archived: autoArchive
      }
    }
  } catch (error) {
    console.error('更新卡片复习结果失败', error)
    return {
      success: false,
      message: '更新失败',
      error: error
    }
  }
}
