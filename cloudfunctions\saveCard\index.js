// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 保存卡片云函数
 * @param {Object} event - 云函数调用参数
 * @param {string} [event.cardId] - 卡片ID，存在则为更新，不存在则为新建
 * @param {string} event.title - 卡片标题
 * @param {string} event.content - 卡片内容
 * @param {string[]} [event.tagIds] - 标签ID数组
 * @param {string} [event.imageUrl] - 卡片图片URL
 * @param {number} [event.reminderTime] - 提醒时间戳
 * @param {boolean} [event.hasEbbinghaus] - 是否设置了艾宾浩斯遗忘曲线提醒
 * @param {Object} context - 云函数上下文
 * @returns {Object} 保存结果
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    const { cardId, title, content, tagIds = [], imageUrl, reminderTime, hasEbbinghaus = false } = event

    // 构建卡片数据
    const dataToSave = {
      title,
      content,
      tagIds,
      hasEbbinghaus,
      updateTime: db.serverDate()
    }

    if (imageUrl) {
      dataToSave.imageUrl = imageUrl;
    }
    // 只有当 reminderTime 是有效的时间戳 (数字) 时才添加
    if (typeof reminderTime === 'number') {
      dataToSave.reminderTime = reminderTime;
    }

    let result

    if (cardId) {
      // 更新现有卡片
      result = await db.collection('cards').doc(cardId).update({
        data: dataToSave
      })

      return {
        success: true,
        message: '卡片更新成功',
        cardId: cardId
      }
    } else {
      // 创建新卡片
      dataToSave._openid = openid
      dataToSave.createTime = db.serverDate()

      // 设置初始复习数据
      // 第一次复习时间根据用户设置确定
      const firstReviewInterval = event.firstReviewInterval || 5 // 默认5分钟后
      const nextReviewTime = new Date()
      nextReviewTime.setTime(nextReviewTime.getTime() + firstReviewInterval * 60 * 1000)

      dataToSave.currentRound = 0 // 当前复习轮次（从0开始）
      dataToSave.totalReviewCount = 0 // 总复习次数
      dataToSave.nextReviewTime = nextReviewTime
      dataToSave.lastReviewTime = null
      dataToSave.reviewResult = null
      dataToSave.reminderSent = false // 提醒发送状态
      dataToSave.reminderSentTime = null

      result = await db.collection('cards').add({
        data: dataToSave
      })

      // 为新创建的卡片安排复习提醒
      try {
        await cloud.callFunction({
          name: 'scheduleCardReminder',
          data: {
            cardId: result._id,
            nextReviewTime: nextReviewTime.toISOString(),
            openid: openid
          }
        })
        console.log('已为新卡片安排复习提醒')
      } catch (error) {
        console.error('安排复习提醒失败', error)
        // 不影响卡片创建的成功
      }

      return {
        success: true,
        message: '卡片创建成功',
        cardId: result._id
      }
    }
  } catch (error) {
    console.error('保存卡片失败', error)
    return {
      success: false,
      error: error
    }
  }
}
