// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
  traceUser: true
})

const db = cloud.database()

/**
 * 检查用户订阅消息状态云函数
 * 优化版本：简化逻辑，移除所有外部依赖
 * @param {Object} event - 云函数调用参数
 * @param {Object} context - 云函数上下文
 * @returns {Object} 检查结果
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    console.log(`检查用户订阅消息状态: ${openid}`)

    if (!openid) {
      return {
        success: false,
        status: 'error',
        message: '无法获取用户openid'
      }
    }

    // 尝试发送一条测试消息来检查授权状态
    const now = new Date()
    const currentTime = now.toISOString().slice(0, 19).replace('T', ' ')

    try {
      const result = await cloud.openapi.subscribeMessage.send({
        touser: openid,
        templateId: 'lNNqgUJU3ITeDC9hlQosOO-a_bF4iF8EM_TpTF3Ap4c',
        page: 'pages/card-list/card-list',
        data: {
          time1: {
            value: currentTime
          },
          thing2: {
            value: '学习计划'
          },
          thing3: {
            value: '订阅消息测试成功'
          }
        }
      })

      console.log('订阅消息发送成功', result)

      // 尝试记录用户的订阅状态（如果失败不影响主要功能）
      try {
        await db.collection('users').where({
          openid: openid
        }).update({
          data: {
            subscribeMessageStatus: 'active',
            lastSubscribeCheck: db.serverDate(),
            updateTime: db.serverDate()
          }
        })
      } catch (updateError) {
        console.log('更新用户状态失败，但不影响主要功能', updateError.message)
      }

      return {
        success: true,
        status: 'active',
        message: '订阅消息测试成功！请查看微信通知'
      }

    } catch (sendError) {
      console.error('订阅消息发送失败', sendError)

      let status = 'unknown'
      let message = '发送失败'

      // 根据错误码判断具体原因
      if (sendError.errCode === 43101) {
        status = 'rejected'
        message = '用户拒绝接收消息'
      } else if (sendError.errCode === 43104) {
        status = 'expired'
        message = '订阅消息授权已过期'
      } else if (sendError.errCode === 40037) {
        status = 'invalid_template'
        message = '模板ID无效'
      } else if (sendError.errCode === -604101) {
        status = 'no_permission'
        message = '云函数没有权限调用此API'
      } else {
        message = sendError.errMsg || '发送失败'
      }

      // 尝试记录用户的订阅状态（如果失败不影响主要功能）
      try {
        await db.collection('users').where({
          openid: openid
        }).update({
          data: {
            subscribeMessageStatus: status,
            lastSubscribeCheck: db.serverDate(),
            lastSubscribeError: sendError.errCode,
            updateTime: db.serverDate()
          }
        })
      } catch (updateError) {
        console.log('更新用户状态失败，但不影响主要功能', updateError.message)
      }

      return {
        success: false,
        status: status,
        message: message,
        errorCode: sendError.errCode || -1,
        needReauth: status === 'rejected' || status === 'expired'
      }
    }

  } catch (error) {
    console.error('检查订阅消息状态失败', error)
    return {
      success: false,
      status: 'error',
      message: '检查失败',
      error: error.message || String(error)
    }
  }
}
