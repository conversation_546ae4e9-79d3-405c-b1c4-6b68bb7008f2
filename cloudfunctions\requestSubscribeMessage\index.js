// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

/**
 * 请求订阅消息授权云函数
 * 用于在小程序端重新请求用户授权订阅消息
 * @param {Object} event - 云函数调用参数
 * @param {Object} context - 云函数上下文
 * @returns {Object} 授权结果
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    // 这个云函数主要用于返回模板ID，实际的授权请求在小程序端进行
    return {
      success: true,
      data: {
        templateId: 'lNNqgUJU3ITeDC9hlQosOO-a_bF4iF8EM_TpTF3Ap4c',
        openid: wxContext.OPENID
      }
    }
  } catch (error) {
    console.error('获取订阅消息配置失败', error)
    return {
      success: false,
      error: error.message
    }
  }
}
