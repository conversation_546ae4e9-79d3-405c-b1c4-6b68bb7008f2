// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
  traceUser: true
})

/**
 * 检查所有云函数状态
 * 用于诊断云函数问题
 * @param {Object} event - 云函数调用参数
 * @param {Object} context - 云函数上下文
 * @returns {Object} 检查结果
 */
exports.main = async (event, context) => {
  try {
    console.log('开始检查云函数状态...')
    
    const results = {
      success: true,
      message: '云函数状态检查完成',
      data: {
        checkTime: new Date().toISOString(),
        functions: []
      }
    }

    // 要检查的云函数列表
    const functionsToCheck = [
      'checkSubscribeStatus',
      'processReminders', 
      'scheduleCardReminder',
      'sendDelayedReminder',
      'triggerReminders',
      'updateReviewResult',
      'requestSubscribeMessage'
    ]

    // 逐个测试云函数
    for (const functionName of functionsToCheck) {
      try {
        console.log(`检查云函数: ${functionName}`)
        
        const startTime = Date.now()
        
        // 调用云函数进行基本测试
        const testResult = await cloud.callFunction({
          name: functionName,
          data: {
            _test: true,
            _checkOnly: true
          }
        })
        
        const endTime = Date.now()
        const duration = endTime - startTime
        
        results.data.functions.push({
          name: functionName,
          status: 'success',
          duration: `${duration}ms`,
          message: '云函数调用成功',
          result: testResult.result
        })
        
      } catch (error) {
        console.error(`云函数 ${functionName} 检查失败:`, error)
        
        results.data.functions.push({
          name: functionName,
          status: 'error',
          message: error.message || String(error),
          errorCode: error.errCode || -1,
          hasTimeUtilsError: error.message && error.message.includes('timeUtils')
        })
      }
    }

    // 统计结果
    const successCount = results.data.functions.filter(f => f.status === 'success').length
    const errorCount = results.data.functions.filter(f => f.status === 'error').length
    const timeUtilsErrors = results.data.functions.filter(f => f.hasTimeUtilsError).length
    
    results.data.summary = {
      total: functionsToCheck.length,
      success: successCount,
      error: errorCount,
      timeUtilsErrors: timeUtilsErrors
    }

    if (timeUtilsErrors > 0) {
      results.message = `发现 ${timeUtilsErrors} 个云函数存在 timeUtils 模块错误，需要重新部署`
      results.needRedeploy = results.data.functions
        .filter(f => f.hasTimeUtilsError)
        .map(f => f.name)
    }

    console.log('云函数状态检查完成')
    return results

  } catch (error) {
    console.error('检查云函数状态失败', error)
    return {
      success: false,
      message: '检查失败',
      error: error.message || String(error)
    }
  }
}
