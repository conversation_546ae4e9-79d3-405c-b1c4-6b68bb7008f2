// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 更新用户信息云函数
 * @param {Object} event - 云函数调用参数
 * @param {string} [event.nickName] - 用户昵称
 * @param {string} [event.avatarUrl] - 用户头像URL
 * @param {Object} context - 云函数上下文
 * @returns {Object} 更新结果
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  try {
    const { nickName, avatarUrl } = event
    
    // 构建更新数据
    const updateData = {
      updateTime: db.serverDate()
    }
    
    if (nickName !== undefined) {
      updateData.nickName = nickName
    }
    
    if (avatarUrl !== undefined) {
      updateData.avatarUrl = avatarUrl
    }
    
    // 更新用户信息
    const result = await db.collection('users')
      .where({
        openid: openid
      })
      .update({
        data: updateData
      })
    
    console.log('更新用户信息成功', result)
    
    return {
      success: true,
      message: '用户信息更新成功',
      data: {
        updated: result.stats.updated
      }
    }
  } catch (error) {
    console.error('更新用户信息失败', error)
    return {
      success: false,
      message: '更新失败',
      error: error
    }
  }
}
