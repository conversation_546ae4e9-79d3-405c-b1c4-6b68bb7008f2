// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

/**
 * 用户登录云函数
 * @param {Object} event - 云函数调用参数
 * @param {string} event.code - 微信登录时获取的 code
 * @param {Object} context - 云函数上下文
 * @returns {Object} 登录结果，包含用户信息和登录状态
 */
exports.main = async (event, context) => {
  // 获取 openid 和用户信息
  const wxContext = cloud.getWXContext()

  // 查询用户是否已存在
  const db = cloud.database()
  const userCollection = db.collection('users')

  try {
    // 查询用户是否已存在
    const userResult = await userCollection.where({
      openid: wxContext.OPENID
    }).get()

    let userData = null

    if (userResult.data.length === 0) {
      // 用户不存在，创建新用户
      const newUser = {
        openid: wxContext.OPENID,
        unionid: wxContext.UNIONID || '',
        appid: wxContext.APPID,
        nickName: '微信用户', // 默认昵称
        avatarUrl: '', // 默认头像为空
        createTime: db.serverDate(),
        lastLoginTime: db.serverDate(),
        isFirstLogin: true // 标记为首次登录
      }

      const addResult = await userCollection.add({
        data: newUser
      })

      userData = {
        ...newUser,
        _id: addResult._id
      }
    } else {
      // 用户已存在，更新登录时间
      userData = userResult.data[0]

      await userCollection.doc(userData._id).update({
        data: {
          lastLoginTime: db.serverDate(),
          isFirstLogin: false // 标记为非首次登录
        }
      })

      // 更新userData中的isFirstLogin状态
      userData.isFirstLogin = false
    }

    // 返回用户信息
    return {
      success: true,
      data: {
        openid: wxContext.OPENID,
        unionid: wxContext.UNIONID,
        appid: wxContext.APPID,
        userData: {
          _id: userData._id,
          nickName: userData.nickName || '微信用户',
          avatarUrl: userData.avatarUrl || '',
          isFirstLogin: userData.isFirstLogin || false,
          createTime: userData.createTime,
          lastLoginTime: userData.lastLoginTime
        }
      }
    }
  } catch (error) {
    console.error('登录失败', error)
    return {
      success: false,
      error: error
    }
  }
}
