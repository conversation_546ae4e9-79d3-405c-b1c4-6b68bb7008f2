.archived-cards-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.filter-dropdown {
  position: sticky;
  top: 0;
  z-index: 100;
}

.card-list {
  flex: 1;
  padding: 0 0 40rpx 0;
  box-sizing: border-box;
  height: calc(100vh - 88rpx);
}

.card-item {
  margin: 0 20rpx 20rpx 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;
  transition: all 0.3s ease;
}

.card-item.archived {
  background: linear-gradient(135deg, #b2bec3 0%, #636e72 100%);
  border: 2rpx solid #636e72;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.card-icon {
  display: flex;
  align-items: center;
}

.icon-placeholder {
  width: 48rpx;
  height: 48rpx;
  margin-right: 12rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.archived-icon {
  background-color: #636e72;
}

.archived-icon::after {
  content: '📦';
  font-size: 24rpx;
}

.icon-text {
  font-size: 24rpx;
  color: #636e72;
  font-weight: 500;
}

.archived-status {
  display: flex;
  align-items: center;
}

.status-text {
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.archived-text {
  background-color: rgba(255, 255, 255, 0.9);
  color: #636e72;
}

.card-content {
  margin-bottom: 16rpx;
}

.card-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.card-meta {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.review-info {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

.create-date {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tag-item {
  font-size: 22rpx;
  background-color: rgba(255, 255, 255, 0.8) !important;
  color: #636e72 !important;
}
