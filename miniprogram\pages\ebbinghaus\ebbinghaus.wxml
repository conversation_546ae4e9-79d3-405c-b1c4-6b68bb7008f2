<view class="ebbinghaus-container">
  <view class="header">
    <view class="title">艾宾浩斯遗忘曲线</view>
    <view class="description">
      根据艾宾浩斯遗忘曲线理论，科学安排复习时间，提高记忆效率。
      此设置将应用于所有新创建的卡片。
    </view>
  </view>

  <view class="timeline">
    <!-- 第一次复习 -->
    <view class="timeline-item">
      <view class="time-point"></view>
      <view class="time-line"></view>
      <view class="time-content">
        <t-cell
          title="第一次复习"
          description="5分钟"
          hover
          bordered="{{false}}"
        >
          <t-switch
            slot="note"
            value="{{settings.review1}}"
            bind:change="onSwitchChange"
            data-key="review1"
          />
        </t-cell>
      </view>
    </view>

    <!-- 第二次复习 -->
    <view class="timeline-item">
      <view class="time-point"></view>
      <view class="time-line"></view>
      <view class="time-content">
        <t-cell
          title="第二次复习"
          description="30分钟"
          hover
          bordered="{{false}}"
        >
          <t-switch
            slot="note"
            value="{{settings.review2}}"
            bind:change="onSwitchChange"
            data-key="review2"
          />
        </t-cell>
      </view>
    </view>

    <!-- 第三次复习 -->
    <view class="timeline-item">
      <view class="time-point"></view>
      <view class="time-line"></view>
      <view class="time-content">
        <t-cell
          title="第三次复习"
          description="12小时"
          hover
          bordered="{{false}}"
        >
          <t-switch
            slot="note"
            value="{{settings.review3}}"
            bind:change="onSwitchChange"
            data-key="review3"
          />
        </t-cell>
      </view>
    </view>

    <!-- 第四次复习 -->
    <view class="timeline-item">
      <view class="time-point"></view>
      <view class="time-line"></view>
      <view class="time-content">
        <t-cell
          title="第四次复习"
          description="1天"
          hover
          bordered="{{false}}"
        >
          <t-switch
            slot="note"
            value="{{settings.review4}}"
            bind:change="onSwitchChange"
            data-key="review4"
          />
        </t-cell>
      </view>
    </view>

    <!-- 第五次复习 -->
    <view class="timeline-item">
      <view class="time-point"></view>
      <view class="time-line"></view>
      <view class="time-content">
        <t-cell
          title="第五次复习"
          description="2天"
          hover
          bordered="{{false}}"
        >
          <t-switch
            slot="note"
            value="{{settings.review5}}"
            bind:change="onSwitchChange"
            data-key="review5"
          />
        </t-cell>
      </view>
    </view>

    <!-- 第六次复习 -->
    <view class="timeline-item">
      <view class="time-point"></view>
      <view class="time-line"></view>
      <view class="time-content">
        <t-cell
          title="第六次复习"
          description="4天"
          hover
          bordered="{{false}}"
        >
          <t-switch
            slot="note"
            value="{{settings.review6}}"
            bind:change="onSwitchChange"
            data-key="review6"
          />
        </t-cell>
      </view>
    </view>

    <!-- 第七次复习 -->
    <view class="timeline-item">
      <view class="time-point"></view>
      <view class="time-line"></view>
      <view class="time-content">
        <t-cell
          title="第七次复习"
          description="7天"
          hover
          bordered="{{false}}"
        >
          <t-switch
            slot="note"
            value="{{settings.review7}}"
            bind:change="onSwitchChange"
            data-key="review7"
          />
        </t-cell>
      </view>
    </view>

    <!-- 第八次复习 -->
    <view class="timeline-item">
      <view class="time-point"></view>
      <view class="time-line"></view>
      <view class="time-content">
        <t-cell
          title="第八次复习"
          description="15天"
          hover
          bordered="{{false}}"
        >
          <t-switch
            slot="note"
            value="{{settings.review8}}"
            bind:change="onSwitchChange"
            data-key="review8"
          />
        </t-cell>
      </view>
    </view>

    <!-- 第九次复习 -->
    <view class="timeline-item">
      <view class="time-point"></view>
      <view class="time-line"></view>
      <view class="time-content">
        <t-cell
          title="第九次复习"
          description="30天"
          hover
          bordered="{{false}}"
        >
          <t-switch
            slot="note"
            value="{{settings.review9}}"
            bind:change="onSwitchChange"
            data-key="review9"
          />
        </t-cell>
      </view>
    </view>

    <!-- 第十次复习 -->
    <view class="timeline-item">
      <view class="time-point"></view>
      <view class="time-line"></view>
      <view class="time-content">
        <t-cell
          title="第十次复习"
          description="60天"
          hover
          bordered="{{false}}"
        >
          <t-switch
            slot="note"
            value="{{settings.review10}}"
            bind:change="onSwitchChange"
            data-key="review10"
          />
        </t-cell>
      </view>
    </view>

    <!-- 第十一次复习 -->
    <view class="timeline-item">
      <view class="time-point"></view>
      <view class="time-line"></view>
      <view class="time-content">
        <t-cell
          title="第十一次复习"
          description="90天"
          hover
          bordered="{{false}}"
        >
          <t-switch
            slot="note"
            value="{{settings.review11}}"
            bind:change="onSwitchChange"
            data-key="review11"
          />
        </t-cell>
      </view>
    </view>

    <!-- 第十二次复习 -->
    <view class="timeline-item">
      <view class="time-point"></view>
      <view class="time-content">
        <t-cell
          title="第十二次复习"
          description="120天"
          hover
          bordered="{{false}}"
        >
          <t-switch
            slot="note"
            value="{{settings.review12}}"
            bind:change="onSwitchChange"
            data-key="review12"
          />
        </t-cell>
      </view>
    </view>
  </view>

  <view class="tips">
    <t-icon name="info-circle" size="36rpx" />
    <text>提示：开启后，系统将在创建卡片后的相应时间点提醒您复习</text>
  </view>

  <view class="save-button-container">
    <t-button theme="primary" size="large" block loading="{{saving}}" bind:tap="saveEbbinghausReminders">
      保存设置
    </t-button>
  </view>
</view>
