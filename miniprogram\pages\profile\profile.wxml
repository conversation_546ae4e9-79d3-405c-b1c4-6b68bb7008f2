<view class="profile-container">
  <!-- 用户信息区域 -->
  <view class="user-info-section">
    <t-avatar
      class="avatar"
      image="{{userInfo.avatarUrl || '/images/default-avatar.png'}}"
      size="large"
      bind:tap="onChangeAvatar"
    />
    <view class="user-name" bindtap="onSetNickName">{{userInfo.nickName || '点击设置昵称'}}</view>
    <view class="user-tip">点击头像或昵称可以修改</view>
  </view>

  <!-- 功能列表 -->
  <t-cell-group title="学习设置">
    <t-cell
      title="复习计划设置"
      arrow
      hover
      note="艾宾浩斯遗忘曲线"
      bind:tap="onNavigateToEbbinghaus"
      leftIcon="schedule"
    />
    <t-cell
      title="消息提醒设置"
      arrow
      hover
      note="{{subscribeMessageEnabled ? '已开启' : '未开启'}}"
      bind:tap="onRequestSubscribeMessage"
      leftIcon="notification"
    />
    <t-cell
      title="测试消息提醒"
      arrow
      hover
      note="测试是否能收到提醒"
      bind:tap="onTestSubscribeMessage"
      leftIcon="sound"
    />
  </t-cell-group>

  <t-cell-group title="卡片管理">
    <t-cell
      title="归档卡片"
      arrow
      hover
      bind:tap="onNavigateToArchivedCards"
      leftIcon="archive"
    />
  </t-cell-group>

  <t-cell-group title="标签管理">
    <t-cell
      title="标签管理"
      arrow
      hover
      bind:tap="onNavigateToTagManage"
      leftIcon="tag"
    />
  </t-cell-group>

  <t-cell-group title="系统管理">
    <t-cell
      title="数据库初始化"
      arrow
      hover
      note="创建必要的数据库集合"
      bind:tap="onNavigateToInitDatabase"
      leftIcon="setting"
    />
  </t-cell-group>

  <t-cell-group title="其他设置">
    <t-cell
      title="关于"
      arrow
      hover
      bind:tap="onShowAbout"
      leftIcon="info-circle"
    />
    <t-cell
      title="清除缓存"
      arrow
      hover
      bind:tap="onClearCache"
      leftIcon="clear"
    />
    <t-cell
      title="退出登录"
      arrow
      hover
      bind:tap="onLogout"
      leftIcon="logout"
      t-class="logout-cell"
    />
  </t-cell-group>

  <view class="version-info">
    <text>版本号：1.0.0</text>
  </view>

  <!-- 自定义TabBar -->
  <custom-tabbar value="profile" />
</view>
