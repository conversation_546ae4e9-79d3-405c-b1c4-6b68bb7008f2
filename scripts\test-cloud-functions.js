/**
 * 云函数测试脚本
 * 用于在小程序中测试修复后的云函数
 * 复制到小程序控制台中运行
 */

// 测试所有云函数状态
function testAllCloudFunctions() {
  console.log('开始测试所有云函数...');
  
  wx.cloud.callFunction({
    name: 'checkCloudFunctions',
    success: (res) => {
      console.log('=== 云函数状态检查结果 ===');
      console.log(res.result);
      
      if (res.result.needRedeploy) {
        console.warn('⚠️ 需要重新部署的云函数:', res.result.needRedeploy);
      }
      
      const summary = res.result.data.summary;
      console.log(`📊 总计: ${summary.total}, 成功: ${summary.success}, 错误: ${summary.error}, timeUtils错误: ${summary.timeUtilsErrors}`);
      
      // 如果没有 timeUtils 错误，继续测试其他功能
      if (summary.timeUtilsErrors === 0) {
        console.log('✅ 没有发现 timeUtils 错误，继续测试其他功能...');
        testSubscribeMessage();
      } else {
        console.error('❌ 仍有 timeUtils 错误，请重新部署相关云函数');
      }
    },
    fail: (err) => {
      console.error('❌ 云函数状态检查失败:', err);
    }
  });
}

// 测试订阅消息功能
function testSubscribeMessage() {
  console.log('测试订阅消息功能...');
  
  wx.cloud.callFunction({
    name: 'checkSubscribeStatus',
    success: (res) => {
      console.log('=== 订阅消息测试结果 ===');
      console.log(res.result);
      
      if (res.result.success) {
        console.log('✅ 订阅消息功能正常');
        testReminderSystem();
      } else {
        console.warn('⚠️ 订阅消息测试失败:', res.result.message);
      }
    },
    fail: (err) => {
      console.error('❌ 订阅消息测试失败:', err);
    }
  });
}

// 测试提醒系统
function testReminderSystem() {
  console.log('测试提醒系统...');
  
  wx.cloud.callFunction({
    name: 'triggerReminders',
    success: (res) => {
      console.log('=== 提醒系统测试结果 ===');
      console.log(res.result);
      
      if (res.result.success) {
        console.log('✅ 提醒系统功能正常');
        testPerformance();
      } else {
        console.warn('⚠️ 提醒系统测试失败:', res.result.message);
      }
    },
    fail: (err) => {
      console.error('❌ 提醒系统测试失败:', err);
    }
  });
}

// 测试性能
function testPerformance() {
  console.log('测试云函数性能...');
  
  wx.cloud.callFunction({
    name: 'testCloudFunctions',
    data: {
      testType: 'all'
    },
    success: (res) => {
      console.log('=== 性能测试结果 ===');
      console.log(res.result);
      
      if (res.result.success) {
        console.log('✅ 性能测试完成');
        console.log('🎉 所有测试完成！云函数修复成功！');
      } else {
        console.warn('⚠️ 性能测试失败:', res.result.message);
      }
    },
    fail: (err) => {
      console.error('❌ 性能测试失败:', err);
    }
  });
}

// 单独测试 sendDelayedReminder 云函数
function testSendDelayedReminder() {
  console.log('单独测试 sendDelayedReminder 云函数...');
  
  // 注意：这个测试需要有效的 cardId，实际使用时请替换
  wx.cloud.callFunction({
    name: 'sendDelayedReminder',
    data: {
      _test: true,
      _checkOnly: true
    },
    success: (res) => {
      console.log('=== sendDelayedReminder 测试结果 ===');
      console.log(res.result);
      
      if (res.result.success || res.result.message.includes('缺少必要参数')) {
        console.log('✅ sendDelayedReminder 云函数正常（参数验证通过）');
      } else {
        console.warn('⚠️ sendDelayedReminder 测试异常:', res.result);
      }
    },
    fail: (err) => {
      console.error('❌ sendDelayedReminder 测试失败:', err);
      
      if (err.errMsg && err.errMsg.includes('timeUtils')) {
        console.error('🚨 仍然存在 timeUtils 错误，请重新部署 sendDelayedReminder 云函数！');
      }
    }
  });
}

// 使用说明
console.log(`
=== 云函数修复测试脚本 ===

使用方法：
1. 在小程序开发者工具的控制台中运行以下命令：

   // 完整测试（推荐）
   testAllCloudFunctions();

   // 单独测试 sendDelayedReminder
   testSendDelayedReminder();

2. 观察控制台输出，确认所有测试通过

3. 如果仍有错误，请按照提示重新部署相关云函数

注意：请确保已经按照修复指南重新部署了所有相关云函数！
`);

// 导出函数供全局使用
window.testAllCloudFunctions = testAllCloudFunctions;
window.testSendDelayedReminder = testSendDelayedReminder;
window.testSubscribeMessage = testSubscribeMessage;
window.testReminderSystem = testReminderSystem;
window.testPerformance = testPerformance;
