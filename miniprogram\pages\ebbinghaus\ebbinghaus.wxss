.ebbinghaus-container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #0052d9;
}

.description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.timeline {
  position: relative;
  margin: 40rpx 0;
}

.timeline-item {
  position: relative;
  padding-left: 40rpx;
  margin-bottom: 20rpx;
}

.time-point {
  position: absolute;
  left: 0;
  top: 40rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #0052d9;
  z-index: 2;
}

.time-line {
  position: absolute;
  left: 7rpx;
  top: 56rpx;
  width: 2rpx;
  height: calc(100% + 20rpx);
  background-color: #0052d9;
  z-index: 1;
}

.timeline-item:last-child .time-line {
  display: none;
}

.time-content {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.tips {
  display: flex;
  align-items: center;
  background-color: #e6f7ff;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-top: 40rpx;
}

.tips text {
  font-size: 24rpx;
  color: #666;
  margin-left: 10rpx;
}

.save-button-container {
  padding: 40rpx 30rpx;
  position: sticky;
  bottom: 0;
  background-color: rgba(245, 245, 245, 0.9);
  backdrop-filter: blur(10px);
  z-index: 10;
}
